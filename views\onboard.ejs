<%- contentFor('HeaderCss') %>
 <%-include("partials/title-meta", { "title": "Onboard" }) %>

<!-- header content goes here -->
<style>
  .hidden {
    display: none;
  }
</style>

<!-- billing CSS starts-->
<style>
  .hidden {
    display: none;
  }
  .status-success {
    color: green;
  }
  .status-error {
    color: red;
  }
</style>

<!-- billing CSS ends -->

<%- contentFor('body') %> 


<div class="container mt-5">
  <div class="progress mb-4">
    <div
      class="progress-bar bg-success progress-bar-striped progress-bar-animated"
      id="progress-bar"
      style="width: 40%"
    ></div>
  </div>

  <div id="screen-1" class="screen">
    <h3 class="mb-3">Business Information</h3>
    <div class="d-flex gap-2 align-items-center">
      <p class="mb-0">Set up your business details.</p>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="businessLogo" class="form-label">Add business logo</label>
          <input type="file" class="form-control" id="businessLogo" />
        </div>
        <div class="mb-3">
          <label for="businessName" class="form-label">Business name *</label>
          <input
            type="text"
            class="form-control"
            id="businessName"
            placeholder="Business name"
          />
        </div>

        <div class="mb-3">
          <label for="description" class="form-label"
            >Business description</label
          >
          <textarea
            class="form-control"
            id="description"
            rows="3"
            placeholder="In a few words, describe your business, products and/or services."
          ></textarea>
        </div>

        <div class="mb-3">
          <label for="businessEmail" class="form-label">Business email *</label>
          <input
            type="email"
            class="form-control"
            id="businessEmail"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="mb-3">
          <label for="businessStructure" class="form-label"
            >Business legal structure *</label
          >
          <select class="form-select" id="businessStructure">
            <option value="LLC">LLC</option>
            <option value="Corporation">Corporation</option>
            <option value="Partnership">Partnership</option>
            <option value="Sole Proprietorship">Sole Proprietorship</option>
          </select>
        </div>
        <div class="mb-3">
          <label for="industry" class="form-label">Industry *</label>
          <select class="form-select" id="industry">
            <option value="Electronics">Electronics</option>
            <option value="Software">Software</option>
            <option value="Retail">Retail</option>
            <option value="Healthcare">Healthcare</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <!-- row col-md-6 ends -->

      <div class="col-md-6">
        <div class="mb-3">
          <label for="businessAddress1" class="form-label"
            >Business address *</label
          >
          <input
            type="text"
            class="form-control"
            id="businessAddress1"
            placeholder="Work/Office Address"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="businessAddress2"
            placeholder="Street address 2 (optional)"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="city"
            placeholder="City"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="state"
            placeholder="State or province"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="postalCode"
            placeholder="Postal code"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="country"
            placeholder="Country"
          />
        </div>
      </div>
    </div>
    <button id="skipBtn" style="all: unset; cursor: pointer">Skip</button>
    <button class="btn btn-primary next-step ms-3" id="next-1">Next</button>
  </div>

 



  <!-- terms and condition starts -->
  <div id="screen-4" class="screen hidden">
    <div id="tc-agreement" class="container mt-5">
      <h3 class="mb-3">Review our T&C Agreement</h3>
      <form id="tc-form">
        <div class="form-check mb-3">
          <input
            class="form-check-input"
            type="checkbox"
            value=""
            id="privacy-policy"
          />
          <label class="form-check-label" for="privacy-policy">
            I have read the
            <a href="https://www.mixcommerce.co/privacy-policy/" target="_blank"
              >privacy policy</a
            >
          </label>
          <div class="invalid-feedback">You must agree before submitting.</div>
        </div>
        <div class="form-check mb-3">
          <input
            class="form-check-input"
            type="checkbox"
            value=""
            id="terms-of-service"
          />
          <label class="form-check-label" for="terms-of-service">
            I have read the
            <a
              href="https://www.mixcommerce.co/terms-of-service/"
              target="_blank"
              >terms of service</a
            >
          </label>
          <div class="invalid-feedback">You must agree before submitting.</div>
        </div>
      </form>
    </div>

    <button class="btn btn-secondary prev-step" id="back-4">Back</button>
    <button class="btn btn-primary next-step" id="finish">Finish</button>
  </div>
  <!-- terms and condistion ends -->
</div>

<!-- body ends here -->
<%- contentFor('FooterJs') %>

<!-- Onboarding JS Starts -->
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"
  integrity="sha512-U2WE1ktpMTuRBPoCFDzomoIorbOyUv0sP8B+INA3EzNAhehbzED1rOJg6bCqPf/Tuposxb5ja/MAUnC8THSbLQ=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>

<script>
  $(document).ready(function () {
      Dropzone.autoDiscover = false;

  let logoURL = "";
  let favIconURL = "";
  // Initialize Dropzone for logo upload
  const logoDropzone = new Dropzone("#logo-dropzone", {
    url: "/api/files/upload/",
    maxFiles: 1,
    maxFilesize: 2, // MB
    acceptedFiles: "image/*",
    success: function (file, response) {
      logoURL = response.file.url;
      console.log("logoURL", logoURL);
      console.log("Logo uploaded successfully:", response);
    },
    error: function (file, response) {
      console.error("Logo upload failed:", response);
    },
  });
    let onboardingData = {};

    function showScreen(screenId) {
      $(".screen").addClass("hidden");
      $(`#${screenId}`).removeClass("hidden");
    }

    $("#next-1").click(function () {
      showScreen("screen-2");
    });

    $("#back-2").click(function () {
      showScreen("screen-1");
    });

    $("#next-2").click(function () {
      onboardingData.businessLogo = $("#businessLogo")[0].files[0];
      onboardingData.businessName = $("#businessName").val();
      onboardingData.businessEmail = $("#businessEmail").val();
      onboardingData.businessStructure = $("#businessStructure").val();
      onboardingData.industry = $("#industry").val();
      onboardingData.description = $("#description").val();
      onboardingData.businessAddress1 = $("#businessAddress1").val();
      onboardingData.businessAddress2 = $("#businessAddress2").val();
      onboardingData.city = $("#city").val();
      onboardingData.state = $("#state").val();
      onboardingData.postalCode = $("#postalCode").val();
      onboardingData.country = $("#country").val();
      showScreen("screen-3");
    });

    $("#back-3").click(function () {
      showScreen("screen-2");
    });

    $(".choose-plan").click(function () {
      onboardingData.plan = $(this).data("plan");
    });

    $("#next-3").click(function () {
      onboardingData.billingFrequency = $(
        'input[name="billingFrequency"]:checked'
      ).val();
      showScreen("screen-4");
    });

    $("#back-4").click(function () {
      showScreen("screen-3");
    });

    $("#next-4").click(function () {
      // Collect billing details data here
      showScreen("screen-5");
    });

    $("#back-5").click(function () {
      showScreen("screen-4");
    });

    $("#finish").click(function () {
      const formData = new FormData();

      // Append all form fields
      for (const key in onboardingData) {
        formData.append(key, onboardingData[key]);
      }

      // Send form data via AJAX
      $.ajax({
        type: "POST",
        url: "/api/onboarding/create",
        data: formData,
        contentType: false, 
        processData: false,
        success: function (response) {
          window.location.href = "/dashboard";
        },
        error: function (error) {
          console.error("Error:", error);
        },
      });
    });
  });
</script>

<!-- skip js starts  -->
<script>
  $(document).ready(function () {
    $("#skipBtn").on("click", function () {
      let onboardingData = {
        businessLogo: "",
        businessName: "Example Business",
        businessEmail: "<EMAIL>",
        businessStructure: "LLC",
        industry: "Software Development",
        businessAddress1: "123 Silicon Valley",
        businessAddress2: "Suite 100",
        city: "San Francisco",
        state: "California",
        postalCode: "94105",
        country: "USA",
        description: "A company focused on cutting-edge software solutions.",
      };

      $.ajax({
        type: "POST",
        url: "/api/onboarding/skip",
        data: JSON.stringify(onboardingData),
        contentType: "application/json",
        success: function (response) {
          window.location.href = "/dashboard";
        },
        error: function (error) {
          console.log(error);
          window.location.href = "/dashboard";
        },
      });
    });
  });
</script>
<!-- skip js ends  -->

<!-- progressbar JS starts -->
<script>
  $(document).ready(function () {
    let currentStep = 1;
    const totalSteps = 5;

    function updateProgressBar() {
      const progress = (currentStep / totalSteps) * 100;
      $("#progress-bar").css("width", progress + "%");
    }

    $(".next-step").click(function () {
      currentStep++;
      if (currentStep > totalSteps) currentStep = totalSteps;
      $(".wizard").removeClass("active");
      $("#step" + currentStep).addClass("active");
      updateProgressBar();
    });

    $(".prev-step").click(function () {
      currentStep--;
      if (currentStep < 1) currentStep = 1;
      $(".wizard").removeClass("active");
      $("#step" + currentStep).addClass("active");
      updateProgressBar();
    });

    $("#wizard-nav a").click(function () {
      const stepId = $(this).attr("href").replace("#step", "");
      currentStep = parseInt(stepId);
      $(".wizard").removeClass("active");
      $("#step" + currentStep).addClass("active");
      updateProgressBar();
    });

    updateProgressBar();
  });
</script>
<!-- progressbar JS Ends -->
