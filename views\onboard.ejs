<%- contentFor('HeaderCss') %>
 <%-include("partials/title-meta", { "title": "Onboard" }) %>

<!-- header content goes here -->
<style>
  .hidden {
    display: none !important;
  }

  .screen {
    display: block;
  }

  .screen.hidden {
    display: none !important;
  }

  /* Dropzone styling */
  .dropzone {
    border: 2px dashed #5156be;
    border-radius: 10px;
    background: #f8f9fa;
    text-align: center;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropzone:hover {
    border-color: #4a4fb7;
    background: #e9ecef;
  }

  .dropzone .dz-message {
    margin: 0;
    pointer-events: none;
  }

  .dropzone .dz-message i {
    color: #5156be;
  }

  /* Prevent dropzone conflicts */
  .dropzone.dz-started .dz-message {
    display: none;
  }

  .dropzone .dz-preview {
    margin: 10px;
  }

  /* Menu preview styling */
  #menuPreview {
    min-height: 60px;
    display: flex;
    align-items: center;
  }

  .menu-item-row {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
  }

  .form-control-color {
    width: 100%;
    height: 40px;
  }
</style>

<!-- billing CSS starts-->
<style>
  .hidden {
    display: none;
  }
  .status-success {
    color: green;
  }
  .status-error {
    color: red;
  }
</style>

<!-- billing CSS ends -->

<%- contentFor('body') %> 


<div class="container mt-5">
  <div class="progress mb-4">
    <div
      class="progress-bar bg-success progress-bar-striped progress-bar-animated"
      id="progress-bar"
      style="width: 33%"
    ></div>
  </div>

  <div id="screen-1" class="screen active">
    <h3 class="mb-3">Business Information</h3>
    <div class="d-flex gap-2 align-items-center">
      <p class="mb-0">Set up your business details.</p>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="businessLogo" class="form-label">Add business logo</label>
          <input type="file" class="form-control" id="businessLogo" />
        </div>
        <div class="mb-3">
          <label for="businessName" class="form-label">Business name *</label>
          <input
            type="text"
            class="form-control"
            id="businessName"
            placeholder="Business name"
          />
        </div>

        <div class="mb-3">
          <label for="description" class="form-label"
            >Business description</label
          >
          <textarea
            class="form-control"
            id="description"
            rows="3"
            placeholder="In a few words, describe your business, products and/or services."
          ></textarea>
        </div>

        <div class="mb-3">
          <label for="businessEmail" class="form-label">Business email *</label>
          <input
            type="email"
            class="form-control"
            id="businessEmail"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="mb-3">
          <label for="businessStructure" class="form-label"
            >Business legal structure *</label
          >
          <select class="form-select" id="businessStructure">
            <option value="LLC">LLC</option>
            <option value="Corporation">Corporation</option>
            <option value="Partnership">Partnership</option>
            <option value="Sole Proprietorship">Sole Proprietorship</option>
          </select>
        </div>
        <div class="mb-3">
          <label for="industry" class="form-label">Industry *</label>
          <select class="form-select" id="industry">
            <option value="Electronics">Electronics</option>
            <option value="Software">Software</option>
            <option value="Retail">Retail</option>
            <option value="Healthcare">Healthcare</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <!-- row col-md-6 ends -->

      <div class="col-md-6">
        <div class="mb-3">
          <label for="businessAddress1" class="form-label"
            >Business address *</label
          >
          <input
            type="text"
            class="form-control"
            id="businessAddress1"
            placeholder="Work/Office Address"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="businessAddress2"
            placeholder="Street address 2 (optional)"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="city"
            placeholder="City"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="state"
            placeholder="State or province"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="postalCode"
            placeholder="Postal code"
          />
        </div>
        <div class="mb-3">
          <input
            type="text"
            class="form-control"
            id="country"
            placeholder="Country"
          />
        </div>
      </div>
    </div>
    <button id="skipBtn" style="all: unset; cursor: pointer">Skip</button>
    <button class="btn btn-primary next-step ms-3" id="next-1">Next</button>
  </div>

  <!-- Public Menu Configuration starts -->
  <div id="screen-2" class="screen hidden">
    <h3 class="mb-3">Public Menu Configuration</h3>
    <div class="d-flex gap-2 align-items-center">
      <p class="mb-0">Set up your public menu appearance and branding.</p>
    </div>

    <div class="row">
      <div class="col-md-6">
        <!-- Logo Upload -->
        <div class="mb-3">
          <label for="logoUrl" class="form-label">Logo URL</label>
          <div id="logo-dropzone" class="dropzone">
            <div class="dz-message">
              <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
              <h5>Drop logo here or click to upload</h5>
              <p class="text-muted">Maximum file size: 2MB</p>
            </div>
          </div>
          <input type="url" class="form-control mt-2" id="logoUrl" placeholder="Or enter logo URL manually" />
        </div>

        <!-- Favicon Upload -->
        <div class="mb-3">
          <label for="favIcon" class="form-label">Favicon URL</label>
          <div id="favicon-dropzone" class="dropzone">
            <div class="dz-message">
              <i class="fas fa-image fa-2x mb-2"></i>
              <h6>Drop favicon here or click to upload</h6>
              <p class="text-muted">Recommended: 32x32px, ICO or PNG format</p>
            </div>
          </div>
          <input type="url" class="form-control mt-2" id="favIcon" placeholder="Or enter favicon URL manually" />
        </div>

        <!-- Font Settings -->
        <div class="mb-3">
          <label for="fontFamily" class="form-label">Font Family</label>
          <select class="form-select" id="fontFamily">
            <option value="Arial, sans-serif">Arial</option>
            <option value="Helvetica, sans-serif">Helvetica</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="Times New Roman, serif">Times New Roman</option>
            <option value="Verdana, sans-serif">Verdana</option>
            <option value="Roboto, sans-serif">Roboto</option>
            <option value="Open Sans, sans-serif">Open Sans</option>
          </select>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="fontWeight" class="form-label">Font Weight</label>
              <select class="form-select" id="fontWeight">
                <option value="normal">Normal</option>
                <option value="bold">Bold</option>
                <option value="lighter">Lighter</option>
                <option value="bolder">Bolder</option>
              </select>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="fontSize" class="form-label">Font Size</label>
              <select class="form-select" id="fontSize">
                <option value="12px">12px</option>
                <option value="14px">14px</option>
                <option value="16px" selected>16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <!-- Color Settings -->
        <div class="mb-3">
          <label for="fontColor" class="form-label">Font Color</label>
          <input type="color" class="form-control form-control-color" id="fontColor" value="#ffffff" />
        </div>

        <div class="mb-3">
          <label for="backgroundColor" class="form-label">Background Color</label>
          <input type="color" class="form-control form-control-color" id="backgroundColor" value="#5156be" />
        </div>

        <!-- Menu Items -->
        <div class="mb-3">
          <label class="form-label">Menu Items</label>
          <div id="menuItemsContainer">
            <div class="menu-item-row mb-2">
              <div class="row">
                <div class="col-md-4">
                  <input type="text" class="form-control menu-text" placeholder="Menu Text" value="Home" />
                </div>
                <div class="col-md-4">
                  <input type="url" class="form-control menu-url" placeholder="URL" value="/p/home" />
                </div>
                <div class="col-md-3">
                  <select class="form-select menu-target">
                    <option value="_blank">New Tab</option>
                    <option value="_self" selected>Same Tab</option>
                  </select>
                </div>
                <div class="col-md-1">
                  <button type="button" class="btn btn-danger btn-sm remove-menu-item">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button type="button" class="btn btn-outline-primary btn-sm" id="addMenuItem">
            <i class="fas fa-plus"></i> Add Menu Item
          </button>
        </div>

        <!-- Preview -->
        <div class="mb-3">
          <label class="form-label">Preview</label>
          <div class="border rounded p-3" id="menuPreview" style="background-color: #5156be; color: #ffffff;">
            <div class="d-flex align-items-center">
              <img src="https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png"
                   alt="Logo" height="30" class="me-3" id="previewLogo" />
              <div class="d-flex gap-3" id="previewMenuItems">
                <a href="#" class="text-decoration-none" style="color: #ffffff;">Home</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button class="btn btn-secondary prev-step" id="back-2">Back</button>
    <button class="btn btn-primary next-step" id="next-2">Next</button>
  </div>
  <!-- Public Menu Configuration ends -->

  <!-- terms and condition starts -->
  <div id="screen-3" class="screen hidden">
    <div id="tc-agreement" class="container mt-5">
      <h3 class="mb-3">Review our T&C Agreement</h3>
      <form id="tc-form">
        <div class="form-check mb-3">
          <input
            class="form-check-input"
            type="checkbox"
            value=""
            id="privacy-policy"
          />
          <label class="form-check-label" for="privacy-policy">
            I have read the
            <a href="https://www.mixcommerce.co/privacy-policy/" target="_blank"
              >privacy policy</a
            >
          </label>
          <div class="invalid-feedback">You must agree before submitting.</div>
        </div>
        <div class="form-check mb-3">
          <input
            class="form-check-input"
            type="checkbox"
            value=""
            id="terms-of-service"
          />
          <label class="form-check-label" for="terms-of-service">
            I have read the
            <a
              href="https://www.mixcommerce.co/terms-of-service/"
              target="_blank"
              >terms of service</a
            >
          </label>
          <div class="invalid-feedback">You must agree before submitting.</div>
        </div>
      </form>
    </div>

    <button class="btn btn-secondary prev-step" id="back-3">Back</button>
    <button class="btn btn-primary next-step" id="finish">Finish</button>
  </div>
  <!-- terms and condistion ends -->
</div>

<!-- body ends here -->
<%- contentFor('FooterJs') %>

<!-- Onboarding JS Starts -->
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"
  integrity="sha512-U2WE1ktpMTuRBPoCFDzomoIorbOyUv0sP8B+INA3EzNAhehbzED1rOJg6bCqPf/Tuposxb5ja/MAUnC8THSbLQ=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>

<script>
  $(document).ready(function () {
    // Disable Dropzone auto-discovery
    Dropzone.autoDiscover = false;

    let logoURL = "";
    let favIconURL = "";
    let logoDropzone = null;
    let faviconDropzone = null;

    // Function to initialize dropzones when screen-2 is shown
    function initializeDropzones() {
      // Only initialize if elements exist and dropzones haven't been created
      if ($("#logo-dropzone").length && !logoDropzone) {
        try {
          logoDropzone = new Dropzone("#logo-dropzone", {
            url: "/api/files/upload/",
            maxFiles: 1,
            maxFilesize: 2, // MB
            acceptedFiles: "image/*",
            addRemoveLinks: true,
            dictDefaultMessage: '<i class="fas fa-cloud-upload-alt fa-3x mb-3"></i><h5>Drop logo here or click to upload</h5><p class="text-muted">Maximum file size: 2MB</p>',
            success: function (file, response) {
              logoURL = response.file.url;
              $("#logoUrl").val(logoURL);
              $("#previewLogo").attr("src", logoURL);
              updatePreview();
              console.log("logoURL", logoURL);
              console.log("Logo uploaded successfully:", response);
            },
            error: function (file, response) {
              console.error("Logo upload failed:", response);
            },
          });
        } catch (error) {
          console.error("Error initializing logo dropzone:", error);
        }
      }

      if ($("#favicon-dropzone").length && !faviconDropzone) {
        try {
          faviconDropzone = new Dropzone("#favicon-dropzone", {
            url: "/api/files/upload/",
            maxFiles: 1,
            maxFilesize: 1, // MB
            acceptedFiles: "image/*,.ico",
            addRemoveLinks: true,
            dictDefaultMessage: '<i class="fas fa-image fa-2x mb-2"></i><h6>Drop favicon here or click to upload</h6><p class="text-muted">Recommended: 32x32px, ICO or PNG format</p>',
            success: function (file, response) {
              favIconURL = response.file.url;
              $("#favIcon").val(favIconURL);
              console.log("favIconURL", favIconURL);
              console.log("Favicon uploaded successfully:", response);
            },
            error: function (file, response) {
              console.error("Favicon upload failed:", response);
            },
          });
        } catch (error) {
          console.error("Error initializing favicon dropzone:", error);
        }
      }
    }

    let onboardingData = {};
    let currentStep = 1;

    // Initialize the onboarding flow
    function initializeOnboarding() {
      console.log("Initializing onboarding...");
      currentStep = 1;
      showScreen("screen-1");
    }

    // Call initialization when document is ready
    initializeOnboarding();

    function showScreen(screenId) {
      console.log("Showing screen:", screenId);
      $(".screen").addClass("hidden");
      $(`#${screenId}`).removeClass("hidden");

      // Update progress bar based on screen
      let progress = 33; // Default for screen-1
      if (screenId === "screen-2") {
        progress = 66;
        // Initialize dropzones when public menu screen is shown
        setTimeout(initializeDropzones, 100);
      }
      if (screenId === "screen-3") progress = 100;

      $("#progress-bar").css("width", progress + "%");
    }

    // Navigation handlers
    $("#next-1").click(function (e) {
      e.preventDefault();
      console.log("Next-1 clicked");

      // Collect business information
      onboardingData.businessLogo = $("#businessLogo")[0].files[0];
      onboardingData.businessName = $("#businessName").val();
      onboardingData.businessEmail = $("#businessEmail").val();
      onboardingData.businessStructure = $("#businessStructure").val();
      onboardingData.industry = $("#industry").val();
      onboardingData.description = $("#description").val();
      onboardingData.businessAddress1 = $("#businessAddress1").val();
      onboardingData.businessAddress2 = $("#businessAddress2").val();
      onboardingData.city = $("#city").val();
      onboardingData.state = $("#state").val();
      onboardingData.postalCode = $("#postalCode").val();
      onboardingData.country = $("#country").val();

      currentStep = 2;
      showScreen("screen-2");
    });

    $("#back-2").click(function (e) {
      e.preventDefault();
      console.log("Back-2 clicked");
      currentStep = 1;
      showScreen("screen-1");
    });

    $("#next-2").click(function (e) {
      e.preventDefault();
      console.log("Next-2 clicked");

      // Collect public menu data
      onboardingData.logoUrl = logoURL || $("#logoUrl").val();
      onboardingData.favIcon = favIconURL || $("#favIcon").val();
      onboardingData.fontColor = $("#fontColor").val();
      onboardingData.backgroundColor = $("#backgroundColor").val();
      onboardingData.fontFamily = $("#fontFamily").val();
      onboardingData.fontWeight = $("#fontWeight").val();
      onboardingData.fontSize = $("#fontSize").val();

      // Collect menu items
      const menuItems = [];
      $(".menu-item-row").each(function() {
        const text = $(this).find(".menu-text").val();
        const url = $(this).find(".menu-url").val();
        const target = $(this).find(".menu-target").val();
        if (text && url) {
          menuItems.push({ text, url, target });
        }
      });
      onboardingData.menuItems = menuItems;

      currentStep = 3;
      showScreen("screen-3");
    });

    $("#back-3").click(function (e) {
      e.preventDefault();
      console.log("Back-3 clicked");
      currentStep = 2;
      showScreen("screen-2");
    });

    // Public Menu Interactive Functionality

    // Add menu item functionality
    $("#addMenuItem").click(function() {
      const newMenuItem = `
        <div class="menu-item-row mb-2">
          <div class="row">
            <div class="col-md-4">
              <input type="text" class="form-control menu-text" placeholder="Menu Text" />
            </div>
            <div class="col-md-4">
              <input type="url" class="form-control menu-url" placeholder="URL" />
            </div>
            <div class="col-md-3">
              <select class="form-select menu-target">
                <option value="_blank">New Tab</option>
                <option value="_self">Same Tab</option>
              </select>
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-danger btn-sm remove-menu-item">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      `;
      $("#menuItemsContainer").append(newMenuItem);
      updatePreview();
    });

    // Remove menu item functionality
    $(document).on("click", ".remove-menu-item", function() {
      $(this).closest(".menu-item-row").remove();
      updatePreview();
    });

    // Update preview when inputs change (using event delegation)
    $(document).on("input change", "#fontColor, #backgroundColor, #logoUrl, .menu-text, .menu-url", function() {
      updatePreview();
    });

    // Handle manual URL input for logo
    $(document).on("input", "#logoUrl", function() {
      const url = $(this).val();
      if (url) {
        logoURL = url;
        updatePreview();
      }
    });

    // Handle manual URL input for favicon
    $(document).on("input", "#favIcon", function() {
      const url = $(this).val();
      if (url) {
        favIconURL = url;
      }
    });

    // Update preview function
    function updatePreview() {
      // Only update if elements exist
      if ($("#menuPreview").length === 0) return;

      const fontColor = $("#fontColor").val() || "#ffffff";
      const backgroundColor = $("#backgroundColor").val() || "#5156be";
      const logoUrl = logoURL || $("#logoUrl").val() || "https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png";

      $("#menuPreview").css({
        "background-color": backgroundColor,
        "color": fontColor
      });

      if ($("#previewLogo").length) {
        $("#previewLogo").attr("src", logoUrl);
      }

      // Update menu items preview
      let menuItemsHtml = "";
      $(".menu-item-row").each(function() {
        const text = $(this).find(".menu-text").val();
        if (text) {
          menuItemsHtml += `<a href="#" class="text-decoration-none me-3" style="color: ${fontColor};">${text}</a>`;
        }
      });

      if ($("#previewMenuItems").length) {
        $("#previewMenuItems").html(menuItemsHtml);
      }
    }

    $("#finish").click(function (e) {
      e.preventDefault();
      console.log("Finish clicked");
      console.log("Final onboarding data:", onboardingData);

      const formData = new FormData();

      // Append all form fields
      for (const key in onboardingData) {
        if (onboardingData[key] !== null && onboardingData[key] !== undefined) {
          if (key === 'menuItems') {
            // Convert menu items to JSON string
            formData.append(key, JSON.stringify(onboardingData[key]));
          } else {
            formData.append(key, onboardingData[key]);
          }
        }
      }

      // Show loading state
      $("#finish").html('<span class="spinner-border spinner-border-sm me-2"></span>Submitting...').prop('disabled', true);

      // Send form data via AJAX
      $.ajax({
        type: "POST",
        url: "/api/onboarding/create",
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
          console.log("Onboarding successful:", response);
          window.location.href = "/dashboard";
        },
        error: function (error) {
          console.error("Error:", error);
          $("#finish").html('Finish').prop('disabled', false);
          alert("Error submitting form. Please try again.");
        },
      });
    });
  });
</script>

<!-- skip js starts  -->
<script>
  $(document).ready(function () {
    $("#skipBtn").on("click", function () {
      let onboardingData = {
        businessLogo: "",
        businessName: "Example Business",
        businessEmail: "<EMAIL>",
        businessStructure: "LLC",
        industry: "Software Development",
        businessAddress1: "123 Silicon Valley",
        businessAddress2: "Suite 100",
        city: "San Francisco",
        state: "California",
        postalCode: "94105",
        country: "USA",
        description: "A company focused on cutting-edge software solutions.",
        // Public menu defaults
        logoUrl: "https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png",
        favIcon: "",
        fontColor: "#ffffff",
        backgroundColor: "#5156be",
        fontFamily: "Arial, sans-serif",
        fontWeight: "normal",
        fontSize: "16px",
        menuItems: [
          { text: "Home", url: "/p/home", target: "_self" },
          { text: "About", url: "/about", target: "_self" },
          { text: "Contact", url: "/contact", target: "_self" }
        ]
      };

      $.ajax({
        type: "POST",
        url: "/api/onboarding/skip",
        data: JSON.stringify(onboardingData),
        contentType: "application/json",
        success: function (response) {
          window.location.href = "/dashboard";
        },
        error: function (error) {
          console.log(error);
          window.location.href = "/dashboard";
        },
      });
    });
  });
</script>
<!-- skip js ends  -->

<!-- progressbar JS removed - now handled in main script -->
