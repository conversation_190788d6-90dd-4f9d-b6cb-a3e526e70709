<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", { "title":"Settings" }) %>

<!-- header content goes here -->
<style>
  .settings-card {
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
  }

  .settings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .settings-card a {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
  }

  .settings-card i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #556ee6;
  }

  .settings-card strong {
    display: block;
    margin-bottom: 8px;
    font-size: 16px;
  }

  .settings-card p {
    font-size: 13px;
    color: #74788d;
  }

  .settings-row {
    display: flex;
    flex-wrap: wrap;
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title", {"title":"Settings", "pagetitle": "Settings" }) %>
<!-- Search Component -->
<div class="mb-4 d-none">
  <input
    type="text"
    class="form-control form-control-lg"
    placeholder="Search  Settings..."
  />
</div>

<!-- body content goes here -->
<div class="container-fluid">
  <div class="row settings-row">
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/homepage/">
          <i style="color: #5156be" class="fas fa-home"></i>
          <strong>HomePage</strong>
          <p>Manage FAQ and other public content.</p>
        </a>
      </div>
    </div>
    
    <!-- scripts settings  -->
       <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/settings-scripts/">
          <i style="color: #5156be" class="fas fa-code"></i>
          <strong>Scripts Settings</strong>
          <p>Manage scripts here.</p>
        </a>
      </div>
    </div>

    <!-- Business Settings -->
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/settings-business/">
          <i style="color: #5156be" class="fas fa-building"></i>
          <strong>Business Settings</strong>
          <p>Manage company name, logo, address, and other details.</p>
        </a>
      </div>
    </div>

    <!-- Custom Variables Settings  -->
    <div class="col-md-3 col-sm-6 mb-4 d-none">
      <div class="settings-card">
        <a href="/settings-custom-variables/">
          <i style="color: #5156be" class="fas fa-cogs"></i>
          <strong>Custom Variables Settings</strong>
          <p>Manage custom variables from here.</p>
        </a>
      </div>
    </div>

    <!-- Plans & Pricing -->
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/plans/">
          <i style="color: #5156be" class="fas fa-dollar-sign"></i>
          <strong>Plans & Pricing</strong>
          <p>Choose your plan and pricing options.</p>
        </a>
      </div>
    </div>

    <!-- Subscription management  -->
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/subscription/">
          <i style="color: #5156be" data-feather="credit-card"></i>
          <strong>Subscription</strong>
          <p>Manage Subscription</p>
        </a>
      </div>
    </div>

    <!-- Profile Settings -->
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href="/settings-profile/">
          <i style="color: #5156be" class="fas fa-user-circle"></i>
          <strong>Profile Settings</strong>
          <p>Update your personal information and preferences.</p>
        </a>
      </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-4">
      <div class="settings-card">
        <a href=" /certificate-mail-editor/">
          <i style="color: #5156be" class="fas fa-envelope"></i>
          <strong>Default Email Content</strong>
          <p>Default Email Content</p>
        </a>
      </div>
      </div>

      <!-- Contact Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-contact/">
            <i style="color: #5156be" class="fas fa-address-book"></i>
            <strong>Contact Settings</strong>
            <p>Manage contact fields and display options.</p>
          </a>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-notification/">
            <i style="color: #5156be" class="fas fa-bell"></i>
            <strong>Notification Settings</strong>
            <p>Configure alerts and notification preferences.</p>
          </a>
        </div>
      </div>

      <!-- Email Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-email/">
            <i style="color: #5156be" class="fas fa-envelope"></i>
            <strong>Email Settings</strong>
            <p>Configure email templates and delivery options.</p>
          </a>
        </div>
      </div>

      <!-- Integration Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-integration/">
            <i style="color: #5156be" class="fas fa-plug"></i>
            <strong>Integration Settings</strong>
            <p>Connect with third-party services and tools.</p>
          </a>
        </div>
      </div>

      <!-- Customization Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-customization/">
            <i style="color: #5156be" class="fas fa-paint-brush"></i>
            <strong>Customization Settings</strong>
            <p>Personalize the appearance and behavior of your system.</p>
          </a>
        </div>
      </div>

      <!-- Data & Backup -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-backup/">
            <i style="color: #5156be" class="fas fa-database"></i>
            <strong>Data & Backup</strong>
            <p>Manage data storage, backups, and restoration.</p>
          </a>
        </div>
      </div>
      <!-- Migration Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-migration/">
            <i style="color: #5156be" class="fas fa-exchange-alt"></i>
            <strong>Migration</strong>
            <p>Manage migration data and restoration.</p>
          </a>
        </div>
      </div>
      <!-- Logs Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/logs/">
            <i style="color: #5156be" class="fas fa-clipboard-list"></i>
            <strong>Logs Settings</strong>
            <p>View and manage system logs and activity records.</p>
          </a>
        </div>
      </div>

      <!-- Security Settings -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-security/">
            <i style="color: #5156be" class="fas fa-shield-alt"></i>
            <strong>Security Settings</strong>
            <p>Configure security options and access controls.</p>
          </a>
        </div>
      </div>

      <!-- Import History -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/import-history/">
            <i style="color: #5156be" class="fas fa-history"></i>
            <strong>Import History</strong>
            <p>View and manage data import records and history.</p>
          </a>
        </div>
      </div>

      <!-- Account Deletion -->
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/settings-account-delete/">
            <i style="color: #5156be" class="fas fa-user-times"></i>
            <strong>Account Deletion</strong>
            <p>Manage account deletion and data removal options.</p>
          </a>
        </div>
      </div>

      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/menu/">
            <i style="color: #5156be" class="fas fa-bars"></i>
            <strong>Public Menu</strong>
            <p>Manage public menus and logo.</p>
          </a>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/social/">
            <i style="color: #5156be" class="fas fa-share-alt"></i>
            <strong>Social Media</strong>
            <p>Manage social media links.</p>
          </a>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-4">
        <div class="settings-card">
          <a href="/branding/">
            <i style="color: #5156be" class="fas fa-user-tie"></i>
            <strong> Branding</strong>
            <p>Manage business logo,description and name.</p>
          </a>
        </div>
      </div>
    </div>
  </div>

  <%- contentFor('FooterJs') %>
</div>
