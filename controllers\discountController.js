const Discount = require("../models/Discount");
const paddleApiUrl = "https://sandbox-api.paddle.com";
const apiKey = process.env.PADDLE_API_KEY;
exports.createDiscount = async (req, res) => {
  try {
    const userId = req.user?.id;
    const {
      description,
      type,
      amount,
      enabled_for_checkout,
      recur,
      maximum_recurring_intervals,
      expires_at,
    } = req.body;
    console.log("req.body", req.body);
    const response = await fetch(`${paddleApiUrl}/discounts`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        description,
        type,
        amount: amount.toString(),
        enabled_for_checkout,
        recur,
        maximum_recurring_intervals,
        expires_at: new Date(expires_at).toISOString(),
      }),
    });
    const parsedResponse = await response.json();
    if (response.ok) {
      const discount = await Discount.create({
        description,
        type,
        amount,
        enabled_for_checkout,
        recur,
        maximum_recurring_intervals,
        expires_at,
        userId,
        businessId: req.user?.businessId,
        discountId: parsedResponse.data.id,
        code: parsedResponse.data.code,
        mode: parsedResponse.data.mode,
        status: parsedResponse.data.status,
        currency_code: parsedResponse.data.currency_code
          ? parsedResponse.data.currency_code
          : "USD",
        times_used: parsedResponse.data.times_used,
      });

      res.status(200).json(discount);
    } else {
      res.status(400).json(parsedResponse);
    }
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

exports.getDiscounts = async (req, res) => {
  try {
    const discounts = await Discount.find({
      businessId: req.user?.businessId,
      userId: req.user?.id,
    });
    return res.status(200).json(discounts);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

exports.updateDiscountShouldBeApplied = async (req, res) => {
  try {
    const { shouldBeApplied } = req.body;
    const { id: discountId } = req.params;
    const discount = await Discount.findOneAndUpdate(
      {
        discountId,
        userId: req.user?.id,
        businessId: req.user?.businessId,
        status: "active",
      },
      { shouldBeApplied },
      { new: true }
    );
    return res.status(200).json(discount);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
