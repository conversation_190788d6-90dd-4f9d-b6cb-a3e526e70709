!(function (e) {
  "use strict";
  var a, t, n;
  localStorage.getItem("minia-language");
  function r() {
    var t = document.querySelectorAll(".counter-value");
    t.forEach(function (r) {
      !(function t() {
        var e = +r.getAttribute("data-target"),
          a = +r.innerText,
          n = e / 250;
        n < 1 && (n = 1),
          a < e
            ? ((r.innerText = (a + n).toFixed(0)), setTimeout(t, 1))
            : (r.innerText = e);
      })();
    });
  }
  function o() {
    for (
      var t = document
          .getElementById("topnav-menu-content")
          .getElementsByTagName("a"),
        e = 0,
        a = t.length;
      e < a;
      e++
    )
      t[e] &&
        t[e].parentElement &&
        "nav-item dropdown active" ===
          t[e].parentElement.getAttribute("class") &&
        (t[e].parentElement.classList.remove("active"),
        t[e].nextElementSibling) &&
        t[e].nextElementSibling.classList.remove("show");
  }
  function i(t) {
    document.getElementById(t).checked = !0;
  }
  function d() {
    document.webkitIsFullScreen ||
      document.mozFullScreen ||
      document.msFullscreenElement ||
      e("body").removeClass("fullscreen-enable");
  }
  if (
    (e("#side-menu").metisMenu(),
    r(),
    (a = document.body.getAttribute("data-sidebar-size")),
    e(window).on("load", function () {
      e(".switch").on("switch-change", function () {
        toggleWeather();
      }),
        1024 <= window.innerWidth &&
          window.innerWidth <= 1366 &&
          (document.body.setAttribute("data-sidebar-size", "sm"),
          i("sidebar-size-small"));
    }),
    e("#vertical-menu-btn").on("click", function (t) {
      t.preventDefault(),
        e("body").toggleClass("sidebar-enable"),
        992 <= e(window).width() &&
          (null == a
            ? null == document.body.getAttribute("data-sidebar-size") ||
              "lg" == document.body.getAttribute("data-sidebar-size")
              ? document.body.setAttribute("data-sidebar-size", "sm")
              : document.body.setAttribute("data-sidebar-size", "lg")
            : "md" == a
            ? "md" == document.body.getAttribute("data-sidebar-size")
              ? document.body.setAttribute("data-sidebar-size", "sm")
              : document.body.setAttribute("data-sidebar-size", "md")
            : "sm" == document.body.getAttribute("data-sidebar-size")
            ? document.body.setAttribute("data-sidebar-size", "lg")
            : document.body.setAttribute("data-sidebar-size", "sm"));
    }),
    e("#sidebar-menu a").each(function () {
      var t = window.location.href.split(/[?#]/)[0];
      this.href == t &&
        (e(this).addClass("active"),
        e(this).parent().addClass("mm-active"),
        e(this).parent().parent().addClass("mm-show"),
        e(this).parent().parent().prev().addClass("mm-active"),
        e(this).parent().parent().parent().addClass("mm-active"),
        e(this).parent().parent().parent().parent().addClass("mm-show"),
        e(this)
          .parent()
          .parent()
          .parent()
          .parent()
          .parent()
          .addClass("mm-active"));
    }),
    e(document).ready(function () {
      var t;
      0 < e("#sidebar-menu").length &&
        0 < e("#sidebar-menu .mm-active .active").length &&
        300 < (t = e("#sidebar-menu .mm-active .active").offset().top) &&
        ((t -= 300),
        e(".vertical-menu .simplebar-content-wrapper").animate(
          { scrollTop: t },
          "slow"
        ));
    }),
    e(".navbar-nav a").each(function () {
      var t = window.location.href.split(/[?#]/)[0];
      this.href == t &&
        (e(this).addClass("active"),
        e(this).parent().addClass("active"),
        e(this).parent().parent().addClass("active"),
        e(this).parent().parent().parent().addClass("active"),
        e(this).parent().parent().parent().parent().addClass("active"),
        e(this).parent().parent().parent().parent().parent().addClass("active"),
        e(this)
          .parent()
          .parent()
          .parent()
          .parent()
          .parent()
          .parent()
          .addClass("active"));
    }),
    e('[data-toggle="fullscreen"]').on("click", function (t) {
      t.preventDefault(),
        e("body").toggleClass("fullscreen-enable"),
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement
          ? document.cancelFullScreen
            ? document.cancelFullScreen()
            : document.mozCancelFullScreen
            ? document.mozCancelFullScreen()
            : document.webkitCancelFullScreen &&
              document.webkitCancelFullScreen()
          : document.documentElement.requestFullscreen
          ? document.documentElement.requestFullscreen()
          : document.documentElement.mozRequestFullScreen
          ? document.documentElement.mozRequestFullScreen()
          : document.documentElement.webkitRequestFullscreen &&
            document.documentElement.webkitRequestFullscreen(
              Element.ALLOW_KEYBOARD_INPUT
            );
    }),
    document.addEventListener("fullscreenchange", d),
    document.addEventListener("webkitfullscreenchange", d),
    document.addEventListener("mozfullscreenchange", d),
    document.getElementById("topnav-menu-content"))
  ) {
    for (
      var s = document
          .getElementById("topnav-menu-content")
          .getElementsByTagName("a"),
        l = 0,
        c = s.length;
      l < c;
      l++
    )
      s[l].onclick = function (t) {
        t &&
          t.target &&
          "#" === t.target.getAttribute("href") &&
          (t.target.parentElement.classList.toggle("active"),
          t.target.nextElementSibling) &&
          t.target.nextElementSibling.classList.toggle("show");
      };
    window.addEventListener("resize", o);
  }
  [].slice
    .call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    .map(function (t) {
      return new bootstrap.Tooltip(t);
    }),
    [].slice
      .call(document.querySelectorAll('[data-bs-toggle="popover"]'))
      .map(function (t) {
        return new bootstrap.Popover(t);
      }),
    [].slice.call(document.querySelectorAll(".toast")).map(function (t) {
      return new bootstrap.Toast(t);
    }),
    window.sessionStorage &&
      ((t = sessionStorage.getItem("is_visited"))
        ? e("#" + t).prop("checked", !0)
        : sessionStorage.setItem("is_visited", "layout-ltr")),
    e(window).on("load", function () {
      e("#status").fadeOut(), e("#preloader").delay(350).fadeOut("slow");
    }),
    (n = document.getElementsByTagName("body")[0]),
    e(".right-bar-toggle").on("click", function (t) {
      e("body").toggleClass("right-bar-enabled");
    }),
    e("#mode-setting-btn").on("click", function (t) {
      console.log("Dark mode toggle clicked");
      n.hasAttribute("data-bs-theme") &&
      "dark" == n.getAttribute("data-bs-theme")
        ? (document.body.setAttribute("data-bs-theme", "light"),
          document.body.setAttribute("data-topbar", "light"),
          document.body.setAttribute("data-sidebar", "light"),
          (n.hasAttribute("data-layout") &&
            "horizontal" == n.getAttribute("data-layout")) ||
            document.body.setAttribute("data-sidebar", "light"),
          i("layout-mode-light"),
          i("sidebar-color-light"),
          i("topbar-color-light"),
          window.renderRadialChart('light'),
          window.localStorage.setItem("theme-mode", "light"))
        : (document.body.setAttribute("data-bs-theme", "dark"),
          document.body.setAttribute("data-topbar", "dark"),
          document.body.setAttribute("data-sidebar", "dark"),
          (n.hasAttribute("data-layout") &&
            "horizontal" == n.getAttribute("data-layout")) ||
            document.body.setAttribute("data-sidebar", "dark"),
          i("layout-mode-dark"),
          i("sidebar-color-dark"),
          i("topbar-color-dark"),
          window.localStorage.setItem("theme-mode", "dark"));
          window.renderRadialChart('dark')
    }),
    e(document).on("click", "body", function (t) {
      0 < e(t.target).closest(".right-bar-toggle, .right-bar").length ||
        e("body").removeClass("right-bar-enabled");
    }),
    n.hasAttribute("data-layout") &&
    "horizontal" == n.getAttribute("data-layout")
      ? (i("layout-horizontal"), e(".sidebar-setting").hide())
      : i("layout-vertical"),
    n.hasAttribute("data-bs-theme") && "dark" == n.getAttribute("data-bs-theme")
      ? i("layout-mode-dark")
      : i("layout-mode-light"),
    n.hasAttribute("data-layout-size") &&
    "boxed" == n.getAttribute("data-layout-size")
      ? i("layout-width-boxed")
      : i("layout-width-fuild"),
    n.hasAttribute("data-layout-scrollable") &&
    "true" == n.getAttribute("data-layout-scrollable")
      ? i("layout-position-scrollable")
      : i("layout-position-fixed"),
    n.hasAttribute("data-topbar") && "dark" == n.getAttribute("data-topbar")
      ? i("topbar-color-dark")
      : i("topbar-color-light"),
    n.hasAttribute("data-sidebar-size") &&
    "sm" == n.getAttribute("data-sidebar-size")
      ? i("sidebar-size-small")
      : n.hasAttribute("data-sidebar-size") &&
        "md" == n.getAttribute("data-sidebar-size")
      ? i("sidebar-size-compact")
      : i("sidebar-size-default"),
    n.hasAttribute("data-sidebar") && "brand" == n.getAttribute("data-sidebar")
      ? i("sidebar-color-brand")
      : n.hasAttribute("data-sidebar") &&
        "dark" == n.getAttribute("data-sidebar")
      ? i("sidebar-color-dark")
      : i("sidebar-color-light"),
    document.getElementsByTagName("html")[0].hasAttribute("dir") &&
    "rtl" == document.getElementsByTagName("html")[0].getAttribute("dir")
      ? i("layout-direction-rtl")
      : i("layout-direction-ltr"),
    e("input[name='layout']").on("change", function () {
      window.location.href =
        "vertical" == e(this).val() ? "index" : "layouts-horizontal";
    }),
    e("input[name='layout-mode']").on("change", function () {
      "light" == e(this).val()
        ? (document.body.setAttribute("data-bs-theme", "light"),
          document.body.setAttribute("data-topbar", "light"),
          document.body.setAttribute("data-sidebar", "light"),
          (n.hasAttribute("data-layout") &&
            "horizontal" == n.getAttribute("data-layout")) ||
            document.body.setAttribute("data-sidebar", "light"),
          i("topbar-color-light"),
          i("sidebar-color-light"))
        : (document.body.setAttribute("data-bs-theme", "dark"),
          document.body.setAttribute("data-topbar", "dark"),
          document.body.setAttribute("data-sidebar", "dark"),
          (n.hasAttribute("data-layout") &&
            "horizontal" == n.getAttribute("data-layout")) ||
            document.body.setAttribute("data-sidebar", "dark"),
          i("topbar-color-dark"),
          i("sidebar-color-dark"));
    }),
    e("input[name='layout-direction']").on("change", function () {
      "ltr" == e(this).val()
        ? (document.getElementsByTagName("html")[0].removeAttribute("dir"),
          document
            .getElementById("bootstrap-style")
            .setAttribute("href", "assets/css/bootstrap.min.css"),
          document
            .getElementById("app-style")
            .setAttribute("href", "assets/css/app.min.css"))
        : (document
            .getElementById("bootstrap-style")
            .setAttribute("href", "assets/css/bootstrap-rtl.min.css"),
          document
            .getElementById("app-style")
            .setAttribute("href", "assets/css/app-rtl.min.css"),
          document.getElementsByTagName("html")[0].setAttribute("dir", "rtl"));
    }),
    Waves.init(),
    e("#checkAll").on("change", function () {
      e(".table-check .form-check-input").prop(
        "checked",
        e(this).prop("checked")
      );
    }),
    e(".table-check .form-check-input").change(function () {
      e(".table-check .form-check-input:checked").length ==
      e(".table-check .form-check-input").length
        ? e("#checkAll").prop("checked", !0)
        : e("#checkAll").prop("checked", !1);
    });

  function updateRadio(radioId) {
    document.getElementById(radioId).checked = true;
  }

  function initThemeMode() {
    console.log("initThemeMode: localStorage:", window.localStorage);

    // Check for saved theme preference or default to 'light'
    const savedTheme = window.localStorage.getItem("theme-mode") || "light";
    const body = document.getElementsByTagName("body")[0];

    console.log("initThemeMode: Loading saved theme:", savedTheme);

    if (savedTheme === "dark") {
      // Apply dark mode
      body.setAttribute("data-bs-theme", "dark");
      body.setAttribute("data-topbar", "dark");
      body.setAttribute("data-sidebar", "dark");
      body.hasAttribute("data-layout") &&
      body.getAttribute("data-layout") == "horizontal"
        ? ""
        : body.setAttribute("data-sidebar", "dark");
      updateRadio("layout-mode-dark");
      updateRadio("sidebar-color-dark");
      updateRadio("topbar-color-dark");
    } else {
      // Apply light mode
      body.setAttribute("data-bs-theme", "light");
      body.setAttribute("data-topbar", "light");
      body.setAttribute("data-sidebar", "light");
      body.hasAttribute("data-layout") &&
      body.getAttribute("data-layout") == "horizontal"
        ? ""
        : body.setAttribute("data-sidebar", "light");
      updateRadio("topbar-color-light");
      updateRadio("sidebar-color-light");
      updateRadio("layout-mode-light");
    }
  }
  initThemeMode();
})(jQuery),
  feather.replace();
