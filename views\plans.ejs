<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", { "title":
"Pricing Plans" }) %> <%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Plans" , "pagetitle": "All" }) %>

<!-- end row -->
<hr class="py-1" />

<div class="my-4">
  <button
    class="btn btn-primary mb-3 d-none"
    data-bs-toggle="modal"
    data-bs-target="#planModal"
    onclick="openModal()"
  >
    Add Plan
  </button>
  <table class="table">
    <thead>
      <tr>
        <th>Name</th>
        <th>ID</th>

        <th>Contacts</th>
        <th>Certificates</th>
        <th>Badges</th>
        <th>Events</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody id="plansTableBody">
      <!-- Rows will be populated here -->
    </tbody>
  </table>
</div>

<!-- View Details Modal -->
<div
  class="modal fade"
  id="viewDetailsModal"
  tabindex="-1"
  aria-labelledby="viewDetailsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="viewDetailsModalLabel">Plan Details</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div id="planDetails">
          <!-- Plan details will be populated here -->
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#choosePlanModal"
        >
          Choose Plan
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>

<!-- plan starts -->

<script>
  let currentPlanId = null;
  let billingCycle = "monthly";
  let paddleToken = undefined;
  let paddlePriceId = "";
  let planTier = "";
  let certificateLimit = 0;
  let planPrice = 0;
  let discountCode = "";
  let activeDiscountId = "";
  $(document).ready(function () {
    // Fetch plans and populate table
    function fetchPlans() {
      $.ajax({
        url: "/api/plans",
        method: "GET",
        success: function (data) {
          const tableBody = $("#plansTableBody");
          tableBody.empty();
          const monthlyPlans = data.plans.filter(
            (plan) => plan.billingCycle === "monthly"
          );
          const yearlyPlans = data.plans.filter(
            (plan) => plan.billingCycle === "yearly"
          );

          monthlyPlans.forEach((plan) => {
            const rowClass =
              plan.status === "active" ? "bg-success text-light" : "";
            tableBody.append(`
                            <tr class="${rowClass}">
                                <td>${plan.name}</td>
                                <td>${plan._id}</td>
                                <td>${plan.contactLimit}</td>
                                <td>${plan.certificateLimit}</td>
                                <td>${plan.badgeLimit}</td>
                                <td>${plan.eventLimit}</td>
                                <td>${plan.status}</td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="viewDetails('${plan._id}')">View Details</button>
                                    
                                </td>
                            </tr>
                        `);
          });
        },
        error: function (error) {
          console.error("Error fetching plans:", error);
        },
      });
    }

    // Fetch plans on page load
    fetchPlans();

    // View plan details
    window.viewDetails = function (id) {
      $.ajax({
        url: `/api/plans/${id}`,
        method: "GET",
        success: function (plan) {
          $("#planDetails").html(`
                        <p><strong>ID:</strong> ${plan._id}</p>
                        <p><strong>Plan Name:</strong> ${plan.name}</p>
                        <p><strong>Price Per Month:</strong> ${plan.pricePerMonth}</p>
                        <p><strong>Description:</strong> ${plan.description}</p>
                        <p><strong>Contact Limit:</strong> ${plan.contactLimit}</p>
                        <p><strong>Badge Limit:</strong> ${plan.badgeLimit}</p>
                        <p><strong>Event Limit:</strong> ${plan.eventLimit}</p>
                        <p><strong>Billing Cycle:</strong> ${plan.billingCycle}</p>
                        <p><strong>Features:</strong> ${plan.features}</p>
                        <p><strong>Premium Onboarding:</strong> ${plan.premiumOnboarding}</p>
                        <p><strong>Dedicated Account Manager:</strong> ${plan.dedicatedAccountManager}</p>
                        <p><strong>Verified Issuer Status:</strong> ${plan.verifiedIssuerStatus}</p>
                        <p><strong>Add-Ons:</strong> ${plan.addOns}</p>
                        <p><strong>Status:</strong> ${plan.status}</p>
                    `);
          $("#viewDetailsModal").modal("show");
        },
        error: function (error) {
          console.error("Error fetching plan details:", error);
        },
      });
    };

    function fetchActiveDiscount() {
      return new Promise((resolve, reject) => {
        $.ajax({
          url: "/api/discounts/active-selected",
          method: "GET",
          success: function (discount) {
            console.log(
              discount.discount.code,
              "response from db active discount"
            );
            if (discount.success) {
              const {
                discount: {
                  code,
                  times_used,
                  maximum_recurring_intervals,
                  discountId,
                },
              } = discount;
              if (
                times_used < maximum_recurring_intervals ||
                !maximum_recurring_intervals
              ) {
                discountCode = code;
              }
              activeDiscountId = discountId;
            }
            resolve();
          },
          error: function (xhr, status, error) {
            console.error("Error fetching active discount:", error);
            reject(error);
          },
        });
      });
    }

    function fetchPaddleToken() {
      $.ajax({
        url: "/api/payments/get-paddle-token",
        method: "GET",
        success: function (response) {
          if (response.success) paddleToken = response.token;

          fetchActiveDiscount()
            .then(() => {
              initializePaddle();
            })
            .catch((error) => {
              console.error("Error fetching active discount:", error);
              initializePaddle();
            });
        },
        error: function (xhr, status, error) {
          console.error("Error fetching Paddle token:", error);
        },
      });
    }

    function savePayment(event) {
      console.log("Paddle event received:", event);

      if (event.name === "checkout.completed") {
        console.log("Full event data:", JSON.stringify(event.data, null, 2));
        const paymentMethod =
          event.data.payment && event.data.payment.method_details
            ? event.data.payment.method_details.type
            : "card";

        const totalAmount = event.data.totals ? event.data.totals.total : 0;

        const transactionId = event.data.transaction_id || "";

        console.log("Transaction ID extracted:", transactionId);

        const data = {
          planName: planTier,
          certificateLimit: certificateLimit,
          paddleTransactionId: transactionId,
          paymentStatus: event.data.status,
          paymentMethod: paymentMethod,
          totalAmountPaid: totalAmount,
          billingCycle: billingCycle,
          customer_id: event.data.customer.id,
          discountId: activeDiscountId,
        };

        console.log("Saving subscription data:", data);

        $.ajax({
          url: "/api/payments/save-subscription",
          method: "POST",
          contentType: "application/json",
          data: JSON.stringify(data),
          success: function (response) {
            console.log("Subscription saved successfully:", response);
            window.location.href = "/subscription";
          },
          error: function (xhr, status, error) {
            console.error("Error saving subscription:", error);
            console.error("Response:", xhr.responseText);
            window.location.href = "/subscription";
          },
        });
      }
    }

    function initializePaddle() {
      if (window.Paddle) {
        Paddle.Environment.set("sandbox");
        Paddle.Initialize({
          token: paddleToken,
          checkout: {
            settings: {
              theme: "light",
              showAddDiscounts: false,
              allowDiscountRemoval: false,
            },
          },

          eventCallback: function (event) {
            savePayment(event);
          },
        });
      }
    }

    fetchPaddleToken();

    // Function to update modal price periods based on billing cycle
    function updatePricePeriods() {
      if (billingCycle === "monthly") {
        $(".modal-price-period").text("per month");
      } else {
        $(".modal-price-period").text("per year");
      }
    }

    // Update price periods when tabs are clicked
    $("#monthly").click(function () {
      billingCycle = "monthly";
      $("body").attr("data-billing-cycle", "monthly");
      updatePricePeriods();
    });

    $("#yearly").click(function () {
      billingCycle = "yearly";
      $("body").attr("data-billing-cycle", "yearly");
      updatePricePeriods();
    });

    // Update price periods when modals are opened
    $(".plan-modal").on("show.bs.modal", function () {
      // Double-check the current billing cycle from the data attribute
      billingCycle = $("body").attr("data-billing-cycle") || "monthly";
      updatePricePeriods();
    });

    // Initialize price periods and data attribute
    $("body").attr("data-billing-cycle", "monthly");
    updatePricePeriods();

    $(document).on("click", "#freePlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = totalCertificateLimit;
      planPrice = price;
      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#standardPlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = totalCertificateLimit;
      planPrice = price;
      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#professionalPlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = totalCertificateLimit;
      planPrice = price;
      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#enterprisePlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = totalCertificateLimit;
      planPrice = price;
      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    function fetchPriceIdOfChoosenPlan(billingCycle, planTier) {
      $.ajax({
        url: `/api/payments/get-price-id?billingCycle=${billingCycle}&planTier=${planTier}`,
        method: "GET",

        success: function (response) {
          console.log(response, "response");
          if (response.success) {
            paddlePriceId = response.priceId;
            openPaddleCheckout();
          }
        },
        error: function (xhr, status, error) {
          console.error("Error fetching price id:", error);
        },
      });
    }

    function openPaddleCheckout() {
      if (window.Paddle) {
        Paddle.Checkout.open({
          items: [
            {
              priceId: paddlePriceId,
              quantity: 1,
            },
          ],
          ...(discountCode ? { discountCode } : {}),
        });
      }
    }

    // Fetch all subscriptions (both active and cancelled)
    function fetchSubscriptionDetails() {
      $.ajax({
        url: "/api/payments/active-subscription",
        method: "GET",
        success: function (response) {
          if (response.success) {
            console.log(response, "response from db  subscriptions");
          }
        },
        error: function (xhr, status, error) {
          console.error("Error fetching subscription details:", error);
        },
      });
    }

    fetchSubscriptionDetails();
  });
</script>
