<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", { "title":"Pricing Plans" }) %>
 <%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Plans" , "pagetitle": "All" }) %>

<!-- end row -->
<hr class="py-1" />

<div class="my-4">

  <table class="table">
    <thead>
      <tr>
        <th>Name</th>
        <th>ID</th>

        <th>Contacts</th>
        <th>Certificates</th>
        <th>Badges</th>
        <th>Events</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody id="plansTableBody">
      <!-- Rows will be populated here -->
    </tbody>
  </table>
</div>

<!-- View Details Modal -->
<div
  class="modal fade"
  id="viewDetailsModal"
  tabindex="-1"
  aria-labelledby="viewDetailsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="viewDetailsModalLabel">Plan Details</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div id="planDetails">
          <!-- Plan details will be populated here -->
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#choosePlanModal"
        >
          Choose Plan
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>

<!-- plan starts -->

<script>
  let currentPlanId = null;
  let billingCycle = "monthly";
  let paddleToken = undefined;
  let paddlePriceId = "";
  let planTier = "";
  let certificateLimit = 0;
  let contactLimit = 0;
  let badgeLimit = 0;
  let eventLimit = 0;
  let planPrice = 0;
  let discountCode = "";
  let activeDiscountId = "";
  $(document).ready(function () {
    // Fetch plans and populate table
    function fetchPlans() {
      $.ajax({
        url: "/api/plans",
        method: "GET",
        success: function (data) {
          const tableBody = $("#plansTableBody");
          tableBody.empty();
          const monthlyPlans = data.plans.filter(
            (plan) => plan.billingCycle === "monthly" || plan.billingCycle === "Monthly"
          );
          const yearlyPlans = data.plans.filter(
            (plan) => plan.billingCycle === "yearly" || plan.billingCycle === "Annually"
          );


          // Display monthly plans
          monthlyPlans.forEach((plan) => {
            const rowClass =
              plan.status === "active" ? "bg-success text-light" : "";
            tableBody.append(`
                            <tr class="${rowClass} monthly-plan-row">
                                <td>${plan.name} <span class="badge bg-info">Monthly</span></td>
                                <td>${plan._id}</td>
                                <td>${plan.contactLimit}</td>
                                <td>${plan.certificateLimit}</td>
                                <td>${plan.badgeLimit}</td>
                                <td>${plan.eventLimit}</td>
                                <td>${plan.status}</td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="viewDetails('${plan._id}')">View Details</button>
                                </td>
                            </tr>
                        `);
          });

          // Display yearly plans
          yearlyPlans.forEach((plan) => {
            const rowClass =
              plan.status === "active" ? "bg-success text-light" : "";
            tableBody.append(`
                            <tr class="${rowClass} yearly-plan-row">
                                <td>${plan.name} <span class="badge bg-warning">Yearly</span></td>
                                <td>${plan._id}</td>
                                <td>${plan.contactLimit}</td>
                                <td>${plan.certificateLimit}</td>
                                <td>${plan.badgeLimit}</td>
                                <td>${plan.eventLimit}</td>
                                <td>${plan.status}</td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="viewDetails('${plan._id}')">View Details</button>
                                </td>
                            </tr>
                        `);
          });
        },
        error: function (error) {
          console.error("Error fetching plans:", error);
        },
      });
    }

    // Fetch plans on page load
    fetchPlans();



    // View plan details
    window.viewDetails = function (id) {
      $.ajax({
        url: `/api/plans/${id}`,
        method: "GET",
        success: function (plan) {
          $("#planDetails").html(`
                        <p><strong>ID:</strong> ${plan._id}</p>
                        <p><strong>Plan Name:</strong> ${plan.name}</p>
                        <p><strong>Price Per Month:</strong> ${plan.pricePerMonth}</p>
                        <p><strong>Description:</strong> ${plan.description}</p>
                        <p><strong>Contact Limit:</strong> ${plan.contactLimit}</p>
                        <p><strong>Badge Limit:</strong> ${plan.badgeLimit}</p>
                        <p><strong>Event Limit:</strong> ${plan.eventLimit}</p>
                        <p><strong>Billing Cycle:</strong> ${plan.billingCycle}</p>
                        <p><strong>Features:</strong> ${plan.features}</p>
                        <p><strong>Premium Onboarding:</strong> ${plan.premiumOnboarding}</p>
                        <p><strong>Dedicated Account Manager:</strong> ${plan.dedicatedAccountManager}</p>
                        <p><strong>Verified Issuer Status:</strong> ${plan.verifiedIssuerStatus}</p>
                        <p><strong>Add-Ons:</strong> ${plan.addOns}</p>
                        <p><strong>Status:</strong> ${plan.status}</p>
                    `);
         
            $("#viewDetailsModal").modal("show");
          
        },
        error: function (error) {
          console.error("Error fetching plan details:", error);
        },
      });
    };

    function fetchActiveDiscount() {
      return new Promise((resolve, reject) => {
        $.ajax({
          url: "/api/discounts/active-selected",
          method: "GET",
          success: function (discount) {
           
            if (discount.success) {
              const {
                discount: {
                  code,
                  times_used,
                  maximum_recurring_intervals,
                  discountId,
                },
              } = discount;
              if (
                times_used < maximum_recurring_intervals ||
                !maximum_recurring_intervals
              ) {
                discountCode = code;
              }
              activeDiscountId = discountId;
            }
            resolve();
          },
          error: function (xhr, status, error) {
            console.error("Error fetching active discount:", error);
            reject(error);
          },
        });
      });
    }

    function fetchPaddleToken() {
      $.ajax({
        url: "/api/payments/get-paddle-token",
        method: "GET",
        success: function (response) {
          if (response.success) paddleToken = response.token;

          fetchActiveDiscount()
            .then(() => {
              initializePaddle();
            })
            .catch((error) => {
              console.error("Error fetching active discount:", error);
              initializePaddle();
            });
        },
        error: function (xhr, status, error) {
          console.error("Error fetching Paddle token:", error);
        },
      });
    }

    function savePayment(event) {
      console.log("Paddle event received:", event);

      if (event.name === "checkout.completed") {
        console.log("Full event data:", JSON.stringify(event.data, null, 2));
        const paymentMethod =
          event.data.payment && event.data.payment.method_details
            ? event.data.payment.method_details.type
            : "card";

        const totalAmount = event.data.totals ? event.data.totals.total : 0;

        const transactionId = event.data.transaction_id || "";

        console.log("Transaction ID extracted:", transactionId);

        const data = {
          planName: planTier,
          certificateLimit: certificateLimit,
          contactLimit: contactLimit,
          badgeLimit: badgeLimit,
          eventLimit: eventLimit,
          paddleTransactionId: transactionId,
          paymentStatus: event.data.status,
          paymentMethod: paymentMethod,
          totalAmountPaid: totalAmount,
          billingCycle: billingCycle,
          customer_id: event.data.customer.id,
          discountId: activeDiscountId,
        };

        console.log("Saving subscription data:", data);

        $.ajax({
          url: "/api/payments/save-subscription",
          method: "POST",
          contentType: "application/json",
          data: JSON.stringify(data),
          success: function (response) {
            console.log("Subscription saved successfully:", response);
            window.location.href = "/subscription";
          },
          error: function (xhr, status, error) {
            console.error("Error saving subscription:", error);
            console.error("Response:", xhr.responseText);
            window.location.href = "/subscription";
          },
        });
      }
    }

    function initializePaddle() {
      if (window.Paddle) {
        Paddle.Environment.set("sandbox");
        Paddle.Initialize({
          token: paddleToken,
          checkout: {
            settings: {
              theme: "light",
              showAddDiscounts: false,
              allowDiscountRemoval: false,
            },
          },

          eventCallback: function (event) {
            savePayment(event);
          },
        });
      }
    }

    fetchPaddleToken();

    // Function to update modal price periods based on billing cycle
    function updatePricePeriods() {
      if (billingCycle === "monthly") {
        $(".modal-price-period").text("per month");
        $(".billing-period-text").text("per month");
        $(".billing-note").text("Billed monthly");
        $(".yearly-savings").hide();
        $(".monthly-badge").show();
        $(".yearly-badge").hide();
      } else {
        $(".modal-price-period").text("per year");
        $(".billing-period-text").text("per year");
        $(".billing-note").text("Billed annually");
        $(".yearly-savings").show();
        $(".monthly-badge").hide();
        $(".yearly-badge").show();
      }
    }

    // Update price periods when tabs are clicked
    $("#monthly").click(function () {
      billingCycle = "monthly";
      $("body").attr("data-billing-cycle", "monthly");
      updatePricePeriods();
      updatePlanVisibility();
      console.log("Switched to Monthly billing cycle");
    });

    $("#yearly").click(function () {
      billingCycle = "yearly";
      $("body").attr("data-billing-cycle", "yearly");
      updatePricePeriods();
      updatePlanVisibility();
      console.log("Switched to Yearly billing cycle");
    });

    // Function to show/hide plans based on billing cycle
    function updatePlanVisibility() {
      if (billingCycle === "monthly") {
        $(".monthly-plan").show();
        $(".yearly-plan").hide();
        $(".monthly-plan-row").show();
        $(".yearly-plan-row").hide();
        $(".billing-period-text").text("per month");
        $(".billing-note").text("Billed monthly");
        console.log("Showing monthly plans");
      } else {
        $(".monthly-plan").hide();
        $(".yearly-plan").show();
        $(".monthly-plan-row").hide();
        $(".yearly-plan-row").show();
        $(".billing-period-text").text("per year");
        $(".billing-note").text("Billed annually");
        console.log("Showing yearly plans");
      }
    }

    // Update price periods when modals are opened
    $(document).on("show.bs.modal", ".plan-modal", function () {
      // Double-check the current billing cycle from the data attribute
      billingCycle = $("body").attr("data-billing-cycle") || "monthly";
      updatePricePeriods();
    });

    // Initialize price periods and data attribute
    $("body").attr("data-billing-cycle", "monthly");
    updatePricePeriods();
    updatePlanVisibility();

    $(document).on("click", "#freePlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit, totalContactLimit, totalBadgeLimit, totalEventLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = parseInt(totalCertificateLimit) || 0;
      contactLimit = parseInt(totalContactLimit) || 0;
      badgeLimit = parseInt(totalBadgeLimit) || 0;
      eventLimit = parseInt(totalEventLimit) || 0;
      planPrice = parseFloat(price) || 0;

      console.log("Free Plan Selected:", {
        planTier, certificateLimit, contactLimit, badgeLimit, eventLimit, planPrice, billingCycle
      });

      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#standardPlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit, totalContactLimit, totalBadgeLimit, totalEventLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = parseInt(totalCertificateLimit) || 0;
      contactLimit = parseInt(totalContactLimit) || 0;
      badgeLimit = parseInt(totalBadgeLimit) || 0;
      eventLimit = parseInt(totalEventLimit) || 0;
      planPrice = parseFloat(price) || 0;

      console.log("Standard Plan Selected:", {
        planTier, certificateLimit, contactLimit, badgeLimit, eventLimit, planPrice, billingCycle
      });

      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#professionalPlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit, totalContactLimit, totalBadgeLimit, totalEventLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = parseInt(totalCertificateLimit) || 0;
      contactLimit = parseInt(totalContactLimit) || 0;
      badgeLimit = parseInt(totalBadgeLimit) || 0;
      eventLimit = parseInt(totalEventLimit) || 0;
      planPrice = parseFloat(price) || 0;

      console.log("Professional Plan Selected:", {
        planTier, certificateLimit, contactLimit, badgeLimit, eventLimit, planPrice, billingCycle
      });

      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    $(document).on("click", "#enterprisePlanButton", function () {
      const planDetails = $(this).data("plan-details");
      const [planName, price, totalCertificateLimit, totalContactLimit, totalBadgeLimit, totalEventLimit] = planDetails.split(",");
      planTier = planName.split(" ")[0];
      certificateLimit = parseInt(totalCertificateLimit) || 0;
      contactLimit = parseInt(totalContactLimit) || 0;
      badgeLimit = parseInt(totalBadgeLimit) || 0;
      eventLimit = parseInt(totalEventLimit) || 0;
      planPrice = parseFloat(price) || 0;

      console.log("Enterprise Plan Selected:", {
        planTier, certificateLimit, contactLimit, badgeLimit, eventLimit, planPrice, billingCycle
      });

      fetchPriceIdOfChoosenPlan(billingCycle, planName);
    });

    function fetchPriceIdOfChoosenPlan(billingCycle, planTier) {
      $.ajax({
        url: `/api/payments/get-price-id?billingCycle=${billingCycle}&planTier=${planTier}`,
        method: "GET",

        success: function (response) {
          console.log(response, "response");
          if (response.success) {
            paddlePriceId = response.priceId;
            openPaddleCheckout();
          }
        },
        error: function (xhr, status, error) {
          console.error("Error fetching price id:", error);
        },
      });
    }

    function openPaddleCheckout() {
      if (window.Paddle) {
        Paddle.Checkout.open({
          items: [
            {
              priceId: paddlePriceId,
              quantity: 1,
            },
          ],
          ...(discountCode ? { discountCode } : {}),
        });
      }
    }

    // Fetch all subscriptions (both active and cancelled)
    function fetchSubscriptionDetails() {
      $.ajax({
        url: "/api/payments/active-subscription",
        method: "GET",
        success: function (response) {
          if (response.success) {
            console.log(response, "response from db  subscriptions");
          }
        },
        error: function (xhr, status, error) {
          console.error("Error fetching subscription details:", error);
        },
      });
    }

    fetchSubscriptionDetails();
  });
</script>

<style>
  /* Enhanced Monthly/Yearly Plan Distinction */
  .billing-cycle-tabs {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    border-radius: 50px;
    padding: 4px;
    margin-bottom: 2rem;
    display: inline-flex;
    box-shadow: 0 4px 15px rgba(81, 86, 190, 0.2);
  }

  .billing-cycle-tabs .nav-link {
    border-radius: 50px !important;
    padding: 12px 30px;
    font-weight: 600;
    color: #ffffff;
    border: none !important;
    background: transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .billing-cycle-tabs .nav-link.active {
    background: #ffffff !important;
    color: #5156be !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .billing-cycle-tabs .nav-link:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
  }

  /* Plan Card Enhancements */
  .plan-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
  }

  .plan-card.monthly-plan {
    border-color: #17a2b8;
  }

  .plan-card.yearly-plan {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
  }

  .plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }

  /* Billing Period Badges */
  .monthly-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
  }

  .yearly-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
  }

  /* Yearly Savings Highlight */
  .yearly-savings {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
    padding: 6px 15px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 700;
    margin-top: 10px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }

  /* Price Display Enhancements */
  .price-display {
    position: relative;
  }

  .price-display.yearly-price::before {
    content: "Save 20%";
    position: absolute;
    top: -25px;
    right: 0;
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
  }

  /* Billing Note Styling */
  .billing-note {
    font-size: 14px;
    color: #6c757d;
    font-style: italic;
    margin-top: 5px;
  }

  .billing-period-text {
    font-weight: 600;
    color: #5156be;
  }

  /* Tab Content Animation */
  .tab-pane {
    animation: fadeInUp 0.5s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Plan Button Enhancements */
  .plan-button {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(81, 86, 190, 0.3);
  }

  .plan-button:hover {
    background: linear-gradient(135deg, #4a4fb7 0%, #5a5fcf 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(81, 86, 190, 0.4);
    color: white;
  }

  /* Plan Table Row Styling */
  .monthly-plan-row {
    border-left: 4px solid #17a2b8;
  }

  .yearly-plan-row {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.05);
  }

  .monthly-plan-row:hover {
    background-color: rgba(23, 162, 184, 0.1) !important;
  }

  .yearly-plan-row:hover {
    background-color: rgba(40, 167, 69, 0.15) !important;
  }

  /* Badge styling in table */
  .badge.bg-info {
    background-color: #17a2b8 !important;
  }

  .badge.bg-warning {
    background-color: #28a745 !important;
    color: white !important;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .billing-cycle-tabs {
      width: 100%;
      justify-content: center;
    }

    .billing-cycle-tabs .nav-link {
      flex: 1;
      text-align: center;
      padding: 10px 20px;
    }

    .yearly-savings {
      font-size: 12px;
      padding: 4px 12px;
    }

    .monthly-badge, .yearly-badge {
      font-size: 11px;
      padding: 3px 10px;
    }
  }
</style>
