<style>
  /* Add branding styles */
  .navbar {
    background-color: #000000; /* Brand color */
  }

  .navbar-brand {
    font-weight: bold;
  }

  .navbar-brand .logo {
    height: 40px; /* Adjust logo size */
    margin-right: 10px;
  }

  .nav-link {
    color: #ffffff !important; /* White text color */
  }

  .nav-link:hover {
    color: #f8f9fa !important; /* Light color on hover */
  }

  /* Custom styling for navbar-toggler-icon to use font color */
  .navbar-toggler-icon {
    background-image: none !important;
    position: relative;
  }

  .navbar-toggler-icon:before {
    content: "☰";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dd-handle {
    cursor: move;
    padding: 10px;
  }

  .item-actions {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .dd-item {
    position: relative;
    padding-right: 100px;
  }
  .dd-empty {
    display: none !important;
  }
  .preview-area {
    border: 2px dashed #ddd;
    padding: 10px;
    text-align: center;
  }

  #logoPreview {
    max-width: 100%;
    height: auto;
    display: none;
  }

  #logoPreview[src] {
    display: inline-block;
  }
  input:disabled,
  input:disabled {
    background-color: #e9ecef !important;
    opacity: 0;
    cursor: not-allowed;
    color: #6c757d;
    border: 1px solid #ced4da;
  }
</style>

<nav id="navbarPublic" class="navbar navbar-expand-lg">
  <div class="container">
    <!-- Brand Logo -->
    <a class="navbar-brand text-white" href="/p/home">
      <img src="" alt="Logo" class="logo" />
    </a>

    <!-- Navbar Toggle for Mobile View -->
    <button
      class="navbar-toggler"
      type="button"
      data-toggle="collapse"
      data-target="#navbarNav"
      aria-controls="navbarNav"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>

    <!-- Navbar Links -->
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav ml-auto"></ul>
    </div>
  </div>
</nav>

<script>
  const defaultItems = [
    { text: "Home", url: "/p/home" },
    { text: "Certificates", url: "/certificate/66c50226fa4ba885b46f124f" },
    { text: "Badges", url: "/badge/66c45d4dc0fc3b4f682e52c7" },
    {
      text: "About",
      url: "https://careers.letsshineksa.com/",
      target: "_blank",
    },
    {
      text: "Career",
      url: "https://careers.letsshineksa.com/",
      target: "_blank",
    },
    {
      text: "Contact",
      url: "https://careers.letsshineksa.com/",
      target: "_blank",
    },
  ];
  $(document).ready(function () {
    $.ajax({
      method: "GET",
      url: "/api/menu/get",
      success: function (result) {
        console.log("Menu settings loaded:", result);
        const menuItemsFromDb = result.items || defaultItems;
        const {
          fontFamily = "Arial, sans-serif",
          fontSize = 16,
          fontWeight = 100,
        } = result.font || {};
        $("#nestableMenu .dd-list").html(
          generateNestableItems(menuItemsFromDb)
        );
        updateNavbar(menuItemsFromDb); // Update navbar on initial load
       updatePageFavicon(result.favIcon);
        $("#navbarPublic").attr(
          "style",
          `background-color: ${result.backgroundColor || "#5156be"}!important`
        );

        $("li a").attr(
          "style",
          `color: ${result.fontColor || "#ffffff"}!important`
        );

        $(".navbar-toggler-icon").attr(
          "style",
          `color: ${result.fontColor}!important`
        );

        $(".navbar, .navbar-brand, .nav-link").css({
          "font-family": fontFamily,
          "font-size": fontSize,
          "font-weight": fontWeight,
        });

        const defaultLogo =
          "https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png";

        // Set logo source
        $(".navbar-brand .logo").attr("src", result.logoUrl || defaultLogo);
      },
      error: function (error) {
        console.log("Error fetching menu settings:", error);
      },
    });
  function updatePageFavicon(url) {
    // Remove existing favicon links
    $('link[rel="icon"], link[rel="shortcut icon"]').remove();

    // Add new favicon link
    $('head').append(`<link rel="icon" type="image/x-icon" href="${url}">`);

    console.log("Page favicon updated to:", url);
  }

    function generateNestableItems(items) {
      return items
        .map(
          (item, index) => `
      <li class="dd-item" data-id="${Date.now() + index}" data-url="${
            item.url
          }">
        <div class="dd-handle">${item.text}</div>
        <div class="item-actions">
          <button class="btn btn-sm btn-warning edit-item"><i class="fas fa-edit"></i></button>
          <button class="btn btn-sm btn-danger delete-item"><i class="fas fa-trash"></i></button>
        </div>
      </li>
    `
        )
        .join("");
    }

    function updateNavbar(items) {
      if (!items) {
        items = JSON.parse(localStorage.getItem("menuItems") || "[]");
      }
      $(".navbar-nav").html(
        items
          .map(
            (item) => `
    <li class="nav-item">
      <a class="nav-link" href="${item.url}" ${
              item.target ? 'target="' + item.target + '"' : ""
            }>
        ${item.text}
      </a>
    </li>
  `
          )
          .join("")
      );
    }
  });
</script>
