const mongoose = require('mongoose');

// Define the schema for the design
const designSchema = new mongoose.Schema({
    designName: {
        type: String,
        required: true
    },
    type: {
        type: String,
        required: false,
    },
    category: {
        type: String,
        required: false,
    },
    tags: {
        type: [String],
        required: false,
    },
    notes: {
        type: [String],
        required: false,
    },
    canvas: {
        type: Object,
        required: false
    },
    pdfURL: {
        type: String,
        required: false,
        default: "/assets/images/design/sample-design-horizontal.pdf"
    },
    imageURL: {
        type: String,
        required: false,
        default: "/assets/images/design/sample-design-horizontal.jpg"
    },
    //certificate related variables starts
    startDate: {
        type: Date,
        required: false
    },
    endDate: {
        type: Date,
        required: false
    },
    location: {
        type: String,
        required: false
    },
    //event's landing page url (if exists)
    url: { 
        type: String,
        required: false
    },
    //certificate related variables ends

    creatorId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    variables: {
        type: Map,
        of: String,
        default: {}
    },
    businessId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business'
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    sharedTo: [
        {
            userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
            access: { type: String, enum: ['public', 'private'], default: 'private' }
        }
    ]
});

// Create indexes for faster search
designSchema.index({ creatorId: 1 });
designSchema.index({ createdAt: 1 });

module.exports = mongoose.model('Design', designSchema);
