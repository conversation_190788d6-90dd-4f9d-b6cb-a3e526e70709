<%- contentFor('HeaderCss') %>
<link
  href="https://cdn.jsdelivr.net/npm/remixicon@4.3.0/fonts/remixicon.css"
  rel="stylesheet"
/>
<link
  href="https://cdnjs.cloudflare.com/ajax/libs/nestable2/1.6.0/jquery.nestable.min.css"
  rel="stylesheet"
/>
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
/>

<style>
  /* Improved navbar styling */
  .navbar {
    background-color: #5156be;
    padding: 15px 0;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .navbar-brand .logo {
    height: 40px;
    margin-right: 10px;
    transition: all 0.3s ease;
  }

  .nav-link {
    /* Default color will be overridden by JavaScript */
    font-weight: 500;
    padding: 8px 15px !important;
    transition: all 0.3s ease;
    border-radius: 4px;
  }

  .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  /* Improved drag and drop styling */
  .dd {
    position: relative;
    display: block;
    margin: 0;
    padding: 0;
    max-width: 100%;
    list-style: none;
    font-size: 13px;
    line-height: 20px;
    overflow-y: visible !important;
    overflow-x: hidden;
  }

  .dd-list {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    list-style: none;
    overflow-y: visible !important;
    overflow-x: hidden;
  }

  .dd-item {
    display: block;
    position: relative;
    margin: 0 0 10px 0;
    padding: 0;
    min-height: 50px;
    font-size: 14px;
    line-height: 20px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }

  .dd-item:hover {
    border-color: #aaa;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .dd-handle {
    display: block;
    height: 50px;
    margin: 0;
    padding: 15px 15px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border-radius: 6px;
    cursor: move;
    background: #f8f9fa;
    transition: background 0.2s ease;
  }

  .dd-handle:hover {
    background: #f0f0f0;
  }

  .dd-item > button {
    position: relative;
    cursor: pointer;
    float: left;
    width: 25px;
    height: 20px;
    margin: 15px 0;
    padding: 0;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: 0;
    background: transparent;
    font-size: 12px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
  }

  .dd-placeholder,
  .dd-empty {
    margin: 5px 0;
    padding: 0;
    min-height: 50px;
    background: #f5f5f5;
    border: 1px dashed #b6bcbf;
    border-radius: 6px;
    box-sizing: border-box;
  }

  .dd-dragel {
    position: absolute;
    pointer-events: none;
    z-index: 9999;
  }

  .dd-dragel > .dd-item .dd-handle {
    margin-top: 0;
    background: #5c7cfa;
    color: #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  }

  /* Item actions styling */
  .item-actions {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }

  .dd-item:hover .item-actions {
    opacity: 1;
  }

  .item-actions .btn {
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
  }

  /* Menu editor panel styling */
  .menu-editor-panel {
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
  }

  .menu-editor-panel .card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
  }

  .menu-editor-panel .card-body {
    padding: 25px;
    overflow-y: visible !important;
  }

  /* Drag indicator */
  .drag-indicator {
    display: inline-block;
    margin-right: 10px;
    color: #aaa;
  }

  /* Empty state */
  .dd-empty-message {
    text-align: center;
    padding: 20px;
    color: #888;
    font-style: italic;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 6px;
    margin: 10px 0;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .dd-item {
      padding-right: 0;
    }

    .item-actions {
      position: relative;
      right: auto;
      top: auto;
      transform: none;
      display: flex;
      justify-content: flex-end;
      padding: 5px 10px 10px;
      background: #f8f9fa;
      border-top: 1px solid #eee;
      border-radius: 0 0 6px 6px;
    }

    .dd-handle {
      border-radius: 6px 6px 0 0;
    }
  }
</style>
<%-include("partials/title-meta", {"title": "Menu" }) %> <%-
contentFor('body')%> <%-include("partials/page-title", {"title": "Public Menus"
,"pagetitle":"Menu" }) %>
<nav style="background: #5156be !important" class="navbar navbar-expand-lg" id="main-navbar">
  <div class="container">
    <!-- Brand Logo -->
    <a class="navbar-brand" href="/p/home" style="color: var(--navbar-font-color, #ffffff) !important;">
      <img src="" alt="Shine Logo" class="logo" />
    </a>

    <!-- Navbar Toggle for Mobile View -->
    <button
      class="navbar-toggler"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#navbarNav"
      aria-controls="navbarNav"
      aria-expanded="false"
      aria-label="Toggle navigation"
      style="color: var(--navbar-font-color, #ffffff) !important;"
    >
      <i class="fas fa-bars" style="color: var(--navbar-font-color, #ffffff) !important;"></i>
    </button>

    <!-- Navbar Links -->
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav ms-auto" style="color: var(--navbar-font-color, #ffffff) !important;"></ul>
    </div>
  </div>
</nav>
<div class="container mt-4" id="menu-editor">
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h4 class="mb-0">Menu Management</h4>
          <p class="text-muted mb-0">
            Customize your website public navigation menu
          </p>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" id="toggle-menu-editor">
            <i class="fas fa-edit me-1"></i> Edit Menu
          </button>
          <button
            style="background: #5156be !important"
            class="btn btn-primary"
            id="edit-header-bg-btn"
          >
            <i class="fas fa-fill-drip me-1"></i> Header Background
          </button>
          <button
            style="background: #5156be !important"
            class="btn btn-primary"
            id="edit-font-color-btn"
          >
            <i class="fas fa-font me-1"></i> Font Color
          </button>
          <button
            style="background: #5156be !important"
            class="btn btn-primary"
            id="edit-font-family-btn"
          >
            <i class="fas fa-text-height me-1"></i> Font Family
          </button>
          <button
            style="background: #5156be !important"
            class="btn btn-primary"
            id="edit-favicon-btn"
          >
            <i class="fas fa-star me-1"></i> Favicon
          </button>
          <button
            style="background: #5156be !important"
            class="btn btn-primary"
            id="edit-logo-btn"
          >
            <i class="fas fa-image me-1"></i> Edit Logo
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="menu-editor-panel mt-3" style="display: none">
    <div class="container-fluid p-0">
      <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8 col-xl-7">
          <div class="card">
            <div
              class="card-header bg-light d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Menu Items</h5>
              <button class="btn btn-success" id="add-menu-item">
                <i class="fas fa-plus me-1"></i> Add Item
              </button>
            </div>
            <div class="card-body">
              <div
                class="dd"
                id="nestableMenu"
                style="width: 100%; overflow-y: visible !important"
              >
                <div class="dd-empty-message" id="emptyMenuMessage">
                  <i class="fas fa-info-circle me-2"></i> No menu items yet.
                  Click "Add Item" to create your first menu item.
                </div>
                <ul class="dd-list">
                  <!-- Menu items will be dynamically inserted here -->
                </ul>
              </div>

              <div class="alert alert-info mt-3">
                <i class="fas fa-lightbulb me-2"></i> <strong>Tip:</strong> Drag
                and drop items to reorder them. Changes are saved automatically.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--  add/edit  menu modal   -->
<div class="modal fade" id="editMenuItemModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="menuModalTitle">Add Menu Item</h5>
        <button
          type="button"
          class="btn-close"
          id="close-menu-modal-x"
          data-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="form-group mb-3">
          <label for="item-text" class="form-label">Menu Text</label>
          <input
            type="text"
            class="form-control"
            id="item-text"
            placeholder="Home, About Us, etc."
          />
          <div class="form-text">
            This text will be displayed in the navigation menu.
          </div>
        </div>
        <div class="form-group mb-3">
          <label for="item-url" class="form-label">URL</label>
          <input
            type="text"
            class="form-control"
            id="item-url"
            placeholder="/page or https://example.com"
          />
          <div class="form-text">Enter a relative path or full URL.</div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          id="close-menu-modal"
          data-dismiss="modal"
        >
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-menu-item">
          <i class="fas fa-save me-1"></i> Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Menu Item Confirmation Modal -->
<div
  class="modal fade"
  id="deleteMenuItemModal"
  tabindex="-1"
  aria-labelledby="deleteMenuItemModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteMenuItemModalLabel">
          Delete Menu Item
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          Are you sure you want to delete "<span id="deleteMenuItemText"></span
          >" from the menu?
        </p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDeleteMenuItem">
          <i class="fas fa-trash me-1"></i> Delete
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Header Background Color Modal -->
<div class="modal fade" id="headerBgColorModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Header Background Color</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <label for="headerBgColorPicker" class="form-label"
            >Choose Background Color</label
          >
          <div class="d-flex align-items-center">
            <input
              type="color"
              class="form-control form-control-color me-2"
              id="headerBgColorPicker"
              value="#5156be"
              title="Choose header background color"
            />
            <input
              type="text"
              class="form-control"
              id="headerBgColorText"
              value="#5156be"
              placeholder="#5156be"
            />
          </div>
          <div class="form-text">
            Select a color from the picker or enter a value in your preferred
            format.
          </div>
        </div>

        <div class="mb-4">
          <label class="form-label">Color Format</label>
          <div
            class="btn-group w-100"
            role="group"
            aria-label="Color format selection"
          >
            <input
              type="radio"
              class="btn-check"
              name="headerBgColorFormat"
              id="headerBgHex"
              value="hex"
              checked
            />
            <label class="btn btn-outline-secondary" for="headerBgHex"
              >HEX</label
            >

            <input
              type="radio"
              class="btn-check"
              name="headerBgColorFormat"
              id="headerBgRgb"
              value="rgb"
            />
            <label class="btn btn-outline-secondary" for="headerBgRgb"
              >RGB</label
            >

            <input
              type="radio"
              class="btn-check"
              name="headerBgColorFormat"
              id="headerBgRgba"
              value="rgba"
            />
            <label class="btn btn-outline-secondary" for="headerBgRgba"
              >RGBA</label
            >
          </div>
        </div>

        <div id="headerBgRgbInputs" class="mb-4 d-none">
          <div class="row g-2">
            <div class="col">
              <label class="form-label small">Red (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="headerBgRed"
                min="0"
                max="255"
                value="81"
              />
            </div>
            <div class="col">
              <label class="form-label small">Green (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="headerBgGreen"
                min="0"
                max="255"
                value="86"
              />
            </div>
            <div class="col">
              <label class="form-label small">Blue (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="headerBgBlue"
                min="0"
                max="255"
                value="190"
              />
            </div>
          </div>
        </div>

        <div id="headerBgRgbaInputs" class="mb-4 d-none">
          <div class="row g-2">
            <div class="col">
              <label class="form-label small">Red</label>
              <input
                type="number"
                class="form-control"
                id="headerBgRedA"
                min="0"
                max="255"
                value="81"
              />
            </div>
            <div class="col">
              <label class="form-label small">Green</label>
              <input
                type="number"
                class="form-control"
                id="headerBgGreenA"
                min="0"
                max="255"
                value="86"
              />
            </div>
            <div class="col">
              <label class="form-label small">Blue</label>
              <input
                type="number"
                class="form-control"
                id="headerBgBlueA"
                min="0"
                max="255"
                value="190"
              />
            </div>
            <div class="col">
              <label class="form-label small">Alpha</label>
              <input
                type="number"
                class="form-control"
                id="headerBgAlpha"
                min="0"
                max="1"
                step="0.1"
                value="1"
              />
            </div>
          </div>
        </div>

        <div class="mt-4">
          <label class="form-label">Preset Colors</label>
          <div class="d-flex flex-wrap gap-2">
            <button
              class="btn p-0 border color-preset"
              data-color="#5156be"
              style="width: 40px; height: 40px; background-color: #5156be"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#3b82f6"
              style="width: 40px; height: 40px; background-color: #3b82f6"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#10b981"
              style="width: 40px; height: 40px; background-color: #10b981"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#f59e0b"
              style="width: 40px; height: 40px; background-color: #f59e0b"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#ef4444"
              style="width: 40px; height: 40px; background-color: #ef4444"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#8b5cf6"
              style="width: 40px; height: 40px; background-color: #8b5cf6"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#ec4899"
              style="width: 40px; height: 40px; background-color: #ec4899"
            ></button>
            <button
              class="btn p-0 border color-preset"
              data-color="#333333"
              style="width: 40px; height: 40px; background-color: #333333"
            ></button>
          </div>
        </div>

        <div class="mt-4 p-3 rounded" style="background-color: #f8f9fa">
          <p class="mb-2 text-muted">Preview:</p>
          <div
            id="headerBgPreview"
            class="p-3 rounded d-flex align-items-center justify-content-center"
            style="background-color: #5156be; height: 60px"
          >
            <span class="text-white fw-bold">Header Background Preview</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-header-bg">
          <i class="fas fa-save me-1"></i> Save Changes
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Font Color Modal -->
<div class="modal fade" id="fontColorModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Font Color</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <label for="fontColorPicker" class="form-label"
            >Choose Font Color</label
          >
          <div class="d-flex align-items-center">
            <input
              type="color"
              class="form-control form-control-color me-2"
              id="fontColorPicker"
              value="#ffffff"
              title="Choose font color"
            />
            <input
              type="text"
              class="form-control"
              id="fontColorText"
              value="#ffffff"
              placeholder="#ffffff"
            />
          </div>
          <div class="form-text">
            Select a color from the picker or enter a value in your preferred
            format.
          </div>
        </div>

        <div class="mb-4">
          <label class="form-label">Color Format</label>
          <div
            class="btn-group w-100"
            role="group"
            aria-label="Color format selection"
          >
            <input
              type="radio"
              class="btn-check"
              name="fontColorFormat"
              id="fontHex"
              value="hex"
              checked
            />
            <label class="btn btn-outline-secondary" for="fontHex">HEX</label>

            <input
              type="radio"
              class="btn-check"
              name="fontColorFormat"
              id="fontRgb"
              value="rgb"
            />
            <label class="btn btn-outline-secondary" for="fontRgb">RGB</label>

            <input
              type="radio"
              class="btn-check"
              name="fontColorFormat"
              id="fontRgba"
              value="rgba"
            />
            <label class="btn btn-outline-secondary" for="fontRgba">RGBA</label>
          </div>
        </div>

        <div id="fontRgbInputs" class="mb-4 d-none">
          <div class="row g-2">
            <div class="col">
              <label class="form-label small">Red (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="fontRed"
                min="0"
                max="255"
                value="255"
              />
            </div>
            <div class="col">
              <label class="form-label small">Green (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="fontGreen"
                min="0"
                max="255"
                value="255"
              />
            </div>
            <div class="col">
              <label class="form-label small">Blue (0-255)</label>
              <input
                type="number"
                class="form-control"
                id="fontBlue"
                min="0"
                max="255"
                value="255"
              />
            </div>
          </div>
        </div>

        <div id="fontRgbaInputs" class="mb-4 d-none">
          <div class="row g-2">
            <div class="col">
              <label class="form-label small">Red</label>
              <input
                type="number"
                class="form-control"
                id="fontRedA"
                min="0"
                max="255"
                value="255"
              />
            </div>
            <div class="col">
              <label class="form-label small">Green</label>
              <input
                type="number"
                class="form-control"
                id="fontGreenA"
                min="0"
                max="255"
                value="255"
              />
            </div>
            <div class="col">
              <label class="form-label small">Blue</label>
              <input
                type="number"
                class="form-control"
                id="fontBlueA"
                min="0"
                max="255"
                value="255"
              />
            </div>
            <div class="col">
              <label class="form-label small">Alpha</label>
              <input
                type="number"
                class="form-control"
                id="fontAlpha"
                min="0"
                max="1"
                step="0.1"
                value="1"
              />
            </div>
          </div>
        </div>

        <div class="mt-4">
          <label class="form-label">Preset Colors</label>
          <div class="d-flex flex-wrap gap-2">
            <button
              class="btn p-0 border color-preset-font"
              data-color="#ffffff"
              style="width: 40px; height: 40px; background-color: #ffffff"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#f8f9fa"
              style="width: 40px; height: 40px; background-color: #f8f9fa"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#e9ecef"
              style="width: 40px; height: 40px; background-color: #e9ecef"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#212529"
              style="width: 40px; height: 40px; background-color: #212529"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#000000"
              style="width: 40px; height: 40px; background-color: #000000"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#ffc107"
              style="width: 40px; height: 40px; background-color: #ffc107"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#0dcaf0"
              style="width: 40px; height: 40px; background-color: #0dcaf0"
            ></button>
            <button
              class="btn p-0 border color-preset-font"
              data-color="#20c997"
              style="width: 40px; height: 40px; background-color: #20c997"
            ></button>
          </div>
        </div>

        <div class="mt-4 p-3 rounded" style="background-color: #f8f9fa">
          <p class="mb-2 text-muted">Preview:</p>
          <div
            id="fontColorPreview"
            class="p-3 rounded d-flex align-items-center justify-content-center"
            style="background-color: #5156be; height: 60px"
          >
            <span style="color: #ffffff" class="fw-bold"
              >Font Color Preview</span
            >
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-font-color">
          <i class="fas fa-save me-1"></i> Save Changes
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Font Family Modal -->
<div class="modal fade" id="fontFamilyModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Change Font Family</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <label for="fontFamilySelect" class="form-label">Select Font Family</label>
          <select class="form-select" id="fontFamilySelect">
            <option value="Arial, sans-serif" style="font-family: Arial, sans-serif;">Arial</option>
            <option value="'Helvetica Neue', Helvetica, Arial, sans-serif" style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Helvetica</option>
            <option value="'Times New Roman', Times, serif" style="font-family: 'Times New Roman', Times, serif;">Times New Roman</option>
            <option value="Georgia, serif" style="font-family: Georgia, serif;">Georgia</option>
            <option value="'Courier New', Courier, monospace" style="font-family: 'Courier New', Courier, monospace;">Courier New</option>
            <option value="Verdana, Geneva, sans-serif" style="font-family: Verdana, Geneva, sans-serif;">Verdana</option>
            <option value="'Open Sans', sans-serif" style="font-family: 'Open Sans', sans-serif;">Open Sans</option>
            <option value="'Roboto', sans-serif" style="font-family: 'Roboto', sans-serif;">Roboto</option>
            <option value="'Lato', sans-serif" style="font-family: 'Lato', sans-serif;">Lato</option>
            <option value="'Montserrat', sans-serif" style="font-family: 'Montserrat', sans-serif;">Montserrat</option>
            <option value="'Poppins', sans-serif" style="font-family: 'Poppins', sans-serif;">Poppins</option>
            <option value="'Source Sans Pro', sans-serif" style="font-family: 'Source Sans Pro', sans-serif;">Source Sans Pro</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="fontWeightSelect" class="form-label">Font Weight</label>
          <select class="form-select" id="fontWeightSelect">
            <option value="normal">Normal</option>
            <option value="bold">Bold</option>
            <option value="lighter">Lighter</option>
            <option value="bolder">Bolder</option>
            <option value="100">100</option>
            <option value="200">200</option>
            <option value="300">300</option>
            <option value="400">400 (Normal)</option>
            <option value="500">500</option>
            <option value="600">600</option>
            <option value="700">700 (Bold)</option>
            <option value="800">800</option>
            <option value="900">900</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="fontSizeInput" class="form-label">Font Size</label>
          <div class="input-group">
            <input type="number" class="form-control" id="fontSizeInput" min="8" max="36" value="16">
            <span class="input-group-text">px</span>
          </div>
        </div>

        <div class="mt-4 p-3 rounded" style="background-color: #f8f9fa">
          <p class="mb-2 text-muted">Preview:</p>
          <div
            id="fontFamilyPreview"
            class="p-3 rounded d-flex align-items-center justify-content-center"
            style="background-color: #5156be; height: 60px; font-family: Arial, sans-serif; font-weight: normal; font-size: 16px;"
          >
            <span style="color: #ffffff" class="fw-bold">Font Family Preview</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-font-family">
          <i class="fas fa-save me-1"></i> Save Changes
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Favicon Modal -->
<div class="modal fade" id="faviconModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Change Favicon</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <label class="form-label">Upload Favicon</label>
          <input type="file" class="form-control" id="faviconFileInput" accept=".ico,.png,.jpg,.jpeg,.gif,.svg">
          <div class="form-text">Recommended: 16x16, 32x32, or 48x48 pixels. Formats: ICO, PNG, JPG, SVG</div>
        </div>

        <div class="mb-4">
          <label for="faviconUrlInput" class="form-label">Or Enter Favicon URL</label>
          <input type="url" class="form-control" id="faviconUrlInput" placeholder="https://example.com/favicon.ico">
        </div>

        <div class="mt-4 p-3 rounded" style="background-color: #f8f9fa">
          <p class="mb-2 text-muted">Preview:</p>
          <div class="d-flex align-items-center justify-content-center p-3 rounded" style="background-color: white; border: 1px solid #dee2e6; min-height: 80px;">
            <div id="faviconPreviewContainer">
              <img id="faviconPreview" src="" alt="Favicon Preview" style="width: 32px; height: 32px; display: none; border: 1px solid #dee2e6;">
              <div id="faviconPlaceholder" class="text-muted text-center">
                <i class="fas fa-star" style="font-size: 32px; color: #dee2e6;"></i>
                <div class="mt-2 small">No favicon selected</div>
              </div>
            </div>
          </div>
          <div class="mt-2 text-center">
            <small class="text-muted">This is how your favicon will appear in browser tabs</small>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-favicon" disabled>
          <i class="fas fa-save me-1"></i> Save Favicon
        </button>
      </div>
    </div>
  </div>
</div>




<!-- edit/add logo modal  -->
<div class="modal fade" id="logoEditorModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Logo</h5>
        <button
          type="button"
          class="btn-close"
          id="close-logo-modal-x"
          data-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Important:</strong> You can only use ONE of these options:
            <ul class="mb-0 mt-2">
              <li><strong>Either</strong> upload a file from your computer</li>
              <li><strong>Or</strong> provide a URL to an existing image</li>
            </ul>
            <p class="mt-2 mb-0">
              <strong>Note:</strong> If you switch between options, your previous selection will be cleared.
            </p>
            <p class="mt-2 mb-0">
              <strong>Suggestion:</strong> For best results, use a <code>.png</code> file with dimensions <strong>272px × 90px</strong>.
            </p>

          </div>
        </div>

        <!-- Simple option selection instead of tabs -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="logoSource"
                id="logoSourceUpload"
                value="upload"
                checked
              />
              <label class="form-check-label" for="logoSourceUpload">
                <i class="fas fa-upload me-1"></i> Upload File
              </label>
            </div>
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="logoSource"
                id="logoSourceUrl"
                value="url"
              />
              <label class="form-check-label" for="logoSourceUrl">
                <i class="fas fa-link me-1"></i> Use URL
              </label>
            </div>
          </div>
        </div>

        <!-- Upload file option -->
        <div id="uploadFileOption" class="mb-3">
          <label for="logoUpload" class="form-label">Select Image File</label>
          <input
            type="file"
            class="form-control"
            id="logoUpload"
            accept="image/*"
          />
          <div class="form-text">
            Recommended size: 200x50 pixels. Max file size: 5MB.
          </div>
        </div>

        <!-- URL option -->
        <div id="urlOption" class="mb-3" style="display: none">
          <label for="logoUrl" class="form-label">Image URL</label>
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-link"></i></span>
            <input
              type="url"
              class="form-control"
              id="logoUrl"
              placeholder="https://example.com/logo.png"
            />
            <button
              class="btn btn-outline-secondary"
              type="button"
              id="clearUrlBtn"
              title="Clear URL"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="form-text">
            Enter a direct link to an image file (must start with http:// or
            https://).
          </div>
        </div>
        <div class="preview-area mt-4 p-3 bg-light text-center rounded">
          <p class="mb-2 text-muted">Preview:</p>
          <img
            id="logoPreview"
            class="img-fluid"
            style="max-height: 100px; display: none"
          />
          <div id="noLogoPreview" class="text-muted">
            <i class="fas fa-image fa-3x mb-2"></i>
            <p>Logo preview will appear here</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          id="close-logo-modal"
          data-dismiss="modal"
        >
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="save-logo">
          <i class="fas fa-save me-1"></i> Save Changes
        </button>
      </div>
    </div>
  </div>
</div>
<%- contentFor('FooterJs') %>
<script src="https://cdnjs.cloudflare.com/ajax/libs/nestable2/1.6.0/jquery.nestable.min.js"></script>

<!-- Add a direct style tag to ensure font color is applied even if JavaScript fails -->
<style id="dynamic-navbar-styles">
  /* These styles will be overridden by JavaScript when it loads */
  /* Using very specific selectors with high specificity to override any existing styles */
  html body .navbar .nav-link,
  html body .navbar .navbar-toggler-icon,
  html body .navbar .dropdown-item,
  html body .navbar .navbar-brand,
  html body .navbar .text-white,
  html body .navbar .text-dark,
  html body .navbar i,
  html body .navbar .bx,
  html body .navbar .navbar-toggler,
  html body .navbar-nav .nav-link,
  html body .navbar-collapse .nav-link,
  html body .navbar-nav .nav-item .nav-link {
    color: #ffffff !important;
  }
</style>

<script>
  // Define global variables for color management
  let headerBgColor = "#5156be"; // Default value, will be updated from API
  let currentFontColor = "#ffffff"; // Default value, will be updated from API

  // Function to update page favicon
  function updatePageFavicon(url) {
    // Remove existing favicon links
    $('link[rel="icon"], link[rel="shortcut icon"]').remove();

    // Add new favicon link
    $('head').append(`<link rel="icon" type="image/x-icon" href="${url}">`);

    
  }

  // Function to apply font color to all navbar elements
  function applyFontColorToNavbar(color) {
    console.log("Applying font color:", color);

    // Create a style rule to apply to all navbar elements with !important
    const fontColorStyle = `color: ${color} !important`;

    // Update the dynamic style tag with high specificity selectors
    const dynamicStyles = `
      /* These styles are dynamically generated with high specificity */
      html body .navbar .nav-link,
      html body .navbar .navbar-toggler-icon,
      html body .navbar .dropdown-item,
      html body .navbar .navbar-brand,
      html body .navbar .text-white,
      html body .navbar .text-dark,
      html body .navbar i,
      html body .navbar .bx,
      html body .navbar .navbar-toggler,
      html body .navbar-nav .nav-link,
      html body .navbar-collapse .nav-link,
      html body .navbar-nav .nav-item .nav-link {
        color: ${color} !important;
      }
    `;
    $("#dynamic-navbar-styles").html(dynamicStyles);

    // Apply inline styles directly to elements with !important
    // This has the highest specificity and will override any CSS

    // Apply to all nav links with multiple approaches
    $(".nav-link").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      // Direct DOM manipulation as a last resort
      this.style.setProperty("color", color, "important");
    });

    // Apply to navbar toggler icon
    $(".navbar-toggler-icon").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Apply to dropdown items
    $(".dropdown-item").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Apply to navbar brand text
    $(".navbar-brand").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Apply to any other text elements in the navbar
    $(".navbar .text-white, .navbar .text-dark").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Apply to any icons in the navbar
    $(".navbar i, .navbar .bx").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Apply to navbar-toggler (hamburger menu)
    $(".navbar-toggler").each(function() {
      $(this).css("color", color);
      $(this).attr("style", fontColorStyle);
      this.style.setProperty("color", color, "important");
    });

    // Set CSS variable for elements using var(--navbar-font-color)
    document.documentElement.style.setProperty('--navbar-font-color', color);

    // Force update by adding and removing a class
    $(".navbar").addClass("font-color-updated").removeClass("font-color-updated");

    // As a last resort, try to find any nav links that might have been missed
    setTimeout(function() {
      $("nav a, .navbar a").each(function() {
        this.style.setProperty("color", color, "important");
      });
    }, 200);

    console.log("Font color applied to navbar elements");
  }

  $(document).ready(function () {
   let menuItemsFromDb=[];
    // Initialize nestable with change callback and improved options
    try {
      // First, make sure the nestable container is properly initialized
      if ($("#nestableMenu").length === 0) {
        console.error("Nestable container not found");
        showToast("Error initializing menu editor", "danger");
        return;
      }

      // Initialize nestable with proper options
      $("#nestableMenu").nestable({
        maxDepth: 1, // Only allow one level of nesting for simplicity
        expandBtnHTML: "",
        collapseBtnHTML: "",
        group: 1, // Add group to ensure proper drag and drop
        listNodeName: 'ul', // Default is ol, make sure it matches your HTML
        itemNodeName: 'li', // Default is li, make sure it matches your HTML
        rootClass: 'dd', // Default class, make sure it matches your HTML
        listClass: 'dd-list', // Default class for lists
        itemClass: 'dd-item', // Default class for items
        dragClass: 'dd-dragel', // Class for dragging element
        handleClass: 'dd-handle', // Class for handle
        placeClass: 'dd-placeholder', // Class for placeholder
        emptyClass: 'dd-empty', // Class for empty list

        // Callback when items are reordered
        callback: function (l, e) {
          try {
            console.log("Nestable callback triggered");
            // Add a small delay to ensure the DOM is updated
            setTimeout(function() {
              saveMenu(); // Save when items are reordered
              showSaveAnimation(); // Show a visual confirmation
            }, 100);
          } catch (error) {
            console.error("Error in nestable callback:", error);
            showToast("Error saving menu order", "danger");
          }
        },

        // Add error handling for drag operations
        onDragStart: function(l, e) {
          console.log("Drag started", e);
          // Make sure the element exists and has required properties
          if (!e) {
            console.warn("Invalid element in drag operation");
            return false; // Prevent drag if element is invalid
          }
          return true; // Allow drag if element is valid
        },

        // Add additional drag event handlers for debugging
        beforeDragStop: function(l, e, p) {
          console.log("Before drag stop", e, p);
          return true;
        },

        afterDragStop: function(l, e, p) {
          console.log("After drag stop", e, p);
          // Trigger the callback manually if needed
          if (typeof this.callback === 'function') {
            this.callback(l, e);
          }
        }
      });

      // Add a manual drag end handler to ensure the callback is triggered
      $(document).on('mouseup', function() {
        if ($('.dd-dragel').length > 0) {
          console.log("Manual drag end detected");
          // The drag operation ended but the callback might not have been triggered
          setTimeout(function() {
            saveMenu();
            showSaveAnimation();
          }, 200);
        }
      });

      console.log("Nestable initialized successfully");
    } catch (error) {
      console.error("Error initializing nestable:", error);
      showToast("Error initializing menu editor", "danger");
    }

    // Toggle editor visibility with animation
    $("#toggle-menu-editor").click(function () {
      $(".menu-editor-panel").slideToggle(300, function () {
        // Update button text based on visibility
        if ($(this).is(":visible")) {
          $("#toggle-menu-editor").html(
            '<i class="fas fa-times me-1"></i> Close Editor'
          );
        } else {
          $("#toggle-menu-editor").html(
            '<i class="fas fa-edit me-1"></i> Edit Menu'
          );
        }
      });
    });

    $.ajax({
      method: "GET",
      url: "/api/menu/get",
      contentType: "application/json",
      data: {
        items: [
          { text: "Home", url: "/p/home" },
          {
            text: "Certificates",
            url: "/certificate/66c50226fa4ba885b46f124f",
          },
          { text: "Badges", url: "/badge/66c45d4dc0fc3b4f682e52c7" },
          {
            text: "About",
            url: "https://careers.letsshineksa.com/",
            target: "_blank",
          },
          {
            text: "Career",
            url: "https://careers.letsshineksa.com/",
            target: "_blank",
          },
          {
            text: "Contact",
            url: "https://careers.letsshineksa.com/",
            target: "_blank",
          },
        ],
      },

      success: function (result) {
        const { items, fontColor, backgroundColor, logoUrl, font, favIcon } = result;

        // Update global variables
        headerBgColor = backgroundColor || "#5156be";
        currentFontColor = fontColor || "#ffffff";

        // Load favicon if exists
        if (favIcon) {
          updatePageFavicon(favIcon);
        }

        // Update font family settings if available
        if (result.font) {
          if (result.font.fontFamily) {
            currentFontFamily = result.font.fontFamily;
            console.log("Loaded font family from database:", currentFontFamily);
          }

          if (result.font.fontWeight) {
            currentFontWeight = result.font.fontWeight;
            console.log("Loaded font weight from database:", currentFontWeight);
          }

          if (result.font.fontSize) {
            currentFontSize = result.font.fontSize;
            // Ensure font size has 'px' unit
            if (typeof currentFontSize === 'number' || !currentFontSize.includes('px')) {
              currentFontSize = currentFontSize + 'px';
            }
            console.log("Loaded font size from database:", currentFontSize);
          }

          // Set flag to indicate font settings are loaded
          fontSettingsLoaded = true;
        }

        console.log("Applying all styling to navbar:", {
          backgroundColor: headerBgColor,
          fontColor: currentFontColor,
          fontFamily: currentFontFamily,
          fontWeight: currentFontWeight,
          fontSize: currentFontSize
        });

        // Apply all styling to navbar
        try {
          // First apply background color and font settings
          $(".navbar").css({
            "background-color": headerBgColor,
            "font-family": currentFontFamily,
            "font-weight": currentFontWeight,
            "font-size": currentFontSize
          });

          // Apply font family and weight to all navbar elements
          $(".navbar *").css({
            "font-family": currentFontFamily,
            "font-weight": currentFontWeight
          });

          // Apply slightly smaller font size to nav links for better appearance
          const linkFontSize = parseInt(currentFontSize) * 0.9 + 'px';
          $(".navbar .nav-link").css("font-size", linkFontSize);

          // Apply font color to all navbar elements
          applyFontColorToNavbar(currentFontColor);

          // Set logo source
          $(".navbar-brand .logo").attr("src", logoUrl || defaultLogo);

          console.log("All navbar styling applied successfully");
        } catch (error) {
          console.error("Error applying navbar styling:", error);
        }

        // Initialize color pickers and controls
        try {
          // Initialize Font Color controls
          $("#fontColorPicker").val(currentFontColor);
          $("#fontColorText").val(currentFontColor);
          $("#fontColorPreview span").css("color", currentFontColor);
          $("#fontColorPreview").css("background-color", headerBgColor);

          // Set initial RGB values based on the hex color
          const initialFontRgb = hexToRgb(currentFontColor);
          $("#fontRed, #fontRedA").val(initialFontRgb.r);
          $("#fontGreen, #fontGreenA").val(initialFontRgb.g);
          $("#fontBlue, #fontBlueA").val(initialFontRgb.b);

          // Initialize Header Background controls
          $("#headerBgColorPicker").val(headerBgColor);
          $("#headerBgColorText").val(headerBgColor);
          $("#headerBgPreview").css("background-color", headerBgColor);
          $("#headerBgPreview span").css("color", currentFontColor);

          // Set initial RGB values based on the hex color
          const initialHeaderRgb = hexToRgb(headerBgColor);
          $("#headerBgRed, #headerBgRedA").val(initialHeaderRgb.r);
          $("#headerBgGreen, #headerBgGreenA").val(initialHeaderRgb.g);
          $("#headerBgBlue, #headerBgBlueA").val(initialHeaderRgb.b);

          console.log("Color controls initialized successfully");
        } catch (error) {
          console.error("Error initializing color controls:", error);
        }

        function hexToRgb(hex) {
          hex = hex.replace("#", "");

          // Parse the hex values
          const r = parseInt(hex.substring(0, 2), 16);
          const g = parseInt(hex.substring(2, 4), 16);
          const b = parseInt(hex.substring(4, 6), 16);

          return { r, g, b };
        }

        function rgbToHex(r, g, b) {
          return (
            "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
          );
        }

        // Handle menu items
        if (items && items.length > 0) {
          $("#nestableMenu .dd-list").html(generateNestableItems(items));
          $("#emptyMenuMessage").hide();
        } else {
          $("#emptyMenuMessage").show();
        }
        updateNavbar(items); // Update navbar on initial load
      },
      error: function (error) {
        console.log("Error fetching menu settings:", error);
        showToast("Error fetching menu settings", "danger");

        // Use default values if API fails
        $(".navbar-brand .logo").attr("src", defaultLogo);

        // Apply all styling with default values
        $(".navbar").css({
          "background-color": headerBgColor,
          "font-family": currentFontFamily,
          "font-weight": currentFontWeight,
          "font-size": currentFontSize
        });

        // Apply to all navbar elements
        $(".navbar *").css({
          "font-family": currentFontFamily,
          "font-weight": currentFontWeight
        });

        // Apply slightly smaller font size to nav links for better appearance
        const linkFontSize = parseInt(currentFontSize) * 0.9 + 'px';
        $(".navbar .nav-link").css("font-size", linkFontSize);

        // Apply font color to all navbar elements with a slight delay to ensure DOM is ready
        setTimeout(function() {
          applyFontColorToNavbar(currentFontColor);
        }, 100);
      },
    });

    // Add new item
    $("#add-menu-item").click(function () {
      // Reset form
      $("#item-text").val("");
      $("#item-url").val("");

      // Update modal title for Add mode
      $("#menuModalTitle").text("Add Menu Item");

      // Clear any previous item ID
      $("#editMenuItemModal").removeData("itemId").modal("show");
    });

    // Save item
    $("#save-menu-item").click(function () {
      const itemId = $("#editMenuItemModal").data("itemId");
      const newText = $("#item-text").val().trim();
      const newUrl = $("#item-url").val().trim();

      // Validate inputs
      if (!newText) {
        $("#item-text").addClass("is-invalid").focus();
        return;
      } else {
        $("#item-text").removeClass("is-invalid");
      }

      if (!newUrl) {
        $("#item-url").addClass("is-invalid").focus();
        return;
      } else {
        $("#item-url").removeClass("is-invalid");
      }

      if (itemId) {
        const text = $("#item-text").val();
        const url = $("#item-url").val();
        $.ajax({
          method: "PUT",
          url: `/api/menu/update/${itemId.toString()}`,
          contentType: "application/json",
          data: JSON.stringify({
            text,
            url,
            target: "_blank",
          }),
          success: function (result) {
            console.log(result);
            $("#nestableMenu .dd-list").html(
              generateNestableItems(result.data.items)
            );
            updateNavbar(result.data.items);
            menuItemsFromDb=result.data.items
            showToast("Menu item updated successfully", "primary");
            $("#editMenuItemModal").removeData("itemId");
            $("#item-text").val("");
            $("#item-url").val("");
            $("#editMenuItemModal").modal("hide");
          },
          error: function (error) {
            console.log(error);
            showToast("Error updating menu item", "danger");
            $("#editMenuItemModal").modal("hide");
            $("#item-text").val("");
            $("#item-url").val("");
            $("#editMenuItemModal").removeData("itemId");
          },
        });
      } else {
        const constructedData = [
          { text: newText, url: newUrl, target: "_blank" },
        ];
        $.ajax({
          method: "POST",
          url: "/api/menu/save",
          contentType: "application/json",
          data: JSON.stringify({
            items: constructedData,
          }),
          success: function (result) {
            console.log(result);
            $("#nestableMenu .dd-list").html(
              generateNestableItems(result.items)
            );
            updateNavbar(result.items);
            menuItemsFromDb=result.items
            showToast("Menu item added successfully", "primary");
          },
          error: function (error) {
            console.log(error);
            showToast("Error adding menu item", "danger");
          },
        });

        // Hide empty message if it was showing
        $("#emptyMenuMessage").hide();
      }

      const menuModal = bootstrap.Modal.getInstance(
        document.getElementById("editMenuItemModal")
      );
      if (menuModal) {
        menuModal.hide();
      }
    });

    // Edit item
    $(document).on("click", ".edit-item", function (e) {
      e.stopPropagation(); // Prevent event bubbling

      const item = $(this).closest(".dd-item");
      const itemText = item.find(".dd-handle-text").text();
      const itemUrl = item.data("url");
      const itemId = item.find(".dd-handle-menuId").text();

      // Update modal title for Edit mode
      $("#menuModalTitle").text("Edit Menu Item");

      // Fill form with current values
      $("#item-text").val(itemText).removeClass("is-invalid");
      $("#item-url").val(itemUrl).removeClass("is-invalid");

      // Store item ID for reference
      $("#editMenuItemModal").data("itemId", itemId).modal("show");
    });

    // Delete item with confirmation
    $(document).on("click", ".delete-item", function (e) {
      e.stopPropagation(); // Prevent event bubbling

      const item = $(this).closest(".dd-item");

      const itemText = item.find(".dd-handle-text").text();
      const itemId = item.find(".dd-handle-menuId").text();

      // Store the item reference for the confirmation modal
      $("#deleteMenuItemModal").data("item", item);
      $("#deleteMenuItemModal").data("itemId", itemId); // Use consistent naming (itemId instead of item-id)

      // Update the confirmation message
      $("#deleteMenuItemText").text(itemText);

      // Show the confirmation modal
      $("#deleteMenuItemModal").modal("show");
    });

    // Save menu to localStorage and update navbar

    // Generate HTML for nestable items
    function generateNestableItems(items) {
      if (!items || !Array.isArray(items) || items.length === 0) {
        console.log("No items to generate nestable HTML");
        return "";
      }

      console.log("Generating nestable items:", items);

      return items
        .map(
          (item, index) => `
          <li class="dd-item" data-id="${item._id}" data-url="${item.url || '#'}">
            <div class="dd-handle">
              <span class="drag-indicator"><i class="fas fa-grip-lines"></i></span>
              <span class="dd-handle-text">${item.text || 'Menu Item'}</span>
              <span style="display:none" class="dd-handle-menuId">${item._id}</span>
            </div>
            <div class="item-actions">
              <button class="btn btn-sm btn-warning edit-item" title="Edit item"><i class="fas fa-edit"></i></button>
              <button class="btn btn-sm btn-danger delete-item" title="Delete item"><i class="fas fa-trash"></i></button>
            </div>
          </li>
        `
        )
        .join("");
    }

    // Update navbar with menu items
    function updateNavbar(items) {
      if (!items) {
        items = JSON.parse(localStorage.getItem("menuItems") || "[]");
      }
      $(".navbar-nav").html(
        items
          .map(
            (item) => `
              <li class="nav-item">
                <a class="nav-link" href="${item.url}">
                  ${item.text}
                </a>
              </li>
            `
          )
          .join("")
      );
    }

    // Save menu function - called when items are reordered
    function saveMenu() {
      try {
        // Get the current menu structure from nestable
        const serializedData = $('#nestableMenu').nestable('serialize');
        console.log("Serialized data:", serializedData);

        if (!serializedData || !Array.isArray(serializedData) || serializedData.length === 0) {
          console.warn("No serialized data available or empty array");
          return;
        }

        // Map the serialized data to the format expected by the API
        // We need to preserve the item properties but update the order
        const reorderedItems = serializedData.map((item, index) => {
          // Find the original item data from menuItemsFromDb using the id
          const originalItem = menuItemsFromDb.find(dbItem => dbItem._id === item.id);

          if (!originalItem) {
            console.warn(`Item with id ${item.id} not found in menuItemsFromDb`);
            // Create a basic item if the original is not found
            return {
              _id: item.id,
              text: $(`li[data-id="${item.id}"] .dd-handle-text`).text().trim(),
              url: $(`li[data-id="${item.id}"]`).data('url') || '#',
              target: "_blank",
              order: index
            };
          }

          // Return the original item with updated order
          return {
            ...originalItem,
            order: index // Add order property based on the new position
          };
        });

        console.log("Reordered items:", reorderedItems);

        // Save to API
        $.ajax({
          method: "POST",
          url: "/api/menu/save?reorder=true",
          contentType: "application/json",
          data: JSON.stringify({
            items: reorderedItems
          }),
          success: function(result) {
            console.log("Menu saved successfully:", result);
            // Update the menuItemsFromDb with the new order
            if (result.items && Array.isArray(result.items)) {
              menuItemsFromDb = result.items;
            }
            updateNavbar(result.items);
          },
          error: function(error) {
            console.log("Error saving menu:", error);
            showToast("Error saving menu order", "danger");
            updateNavbar(serializedData);
          }
        });
      } catch (error) {
        console.error("Error in saveMenu function:", error);
        showToast("Error saving menu order", "danger");
      }
    }

    // Show save animation
    function showSaveAnimation() {
      showToast("Menu saved successfully");
    }

    // Handle confirm delete button click - inside document ready to access saveMenu function
    $("#confirmDeleteMenuItem").on("click", function () {
      const item = $("#deleteMenuItemModal").data("item");
      const itemId = $("#deleteMenuItemModal").data("itemId");

      $.ajax({
        method: "DELETE",
        url: `/api/menu/delete/${itemId.toString()}`,
        success: function (result) {
          console.log(result);
          $("#nestableMenu .dd-list").html(generateNestableItems(result.items));
          updateNavbar(result.items);

          item.fadeOut(300, function () {
            $(this).remove();
          });
          const deleteModal = bootstrap.Modal.getInstance(
            document.getElementById("deleteMenuItemModal")
          );
          if (deleteModal) {
            deleteModal.hide();
          }
          showToast("Menu item deleted successfully");
        },
        error: function (error) {
          console.log(error);
          showToast("Error deleting menu item", "danger");
          item.remove();
        },
      });
    });
  });

  // Modal close handlers using Bootstrap 5 methods
  $("#close-menu-modal, #close-menu-modal-x").on("click", function () {
    const menuModal = bootstrap.Modal.getInstance(
      document.getElementById("editMenuItemModal")
    );
    if (menuModal) {
      menuModal.hide();
    }
  });

  $("#close-logo-modal, #close-logo-modal-x").on("click", function () {
    const logoModal = bootstrap.Modal.getInstance(
      document.getElementById("logoEditorModal")
    );
    if (logoModal) {
      logoModal.hide();
    }
  });
</script>

<style>
  /* Save indicator */
  .save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #5156be;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .save-indicator.show {
    opacity: 1;
    transform: translateY(0);
  }
</style>

<script>
  $(document).ready(function () {
    // Default logo if needed
    const defaultLogo = "https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png";

    // Initialize global variables with default values
    let headerBgColor = "#5156be";
    let currentFontColor = "#ffffff";
    let currentFontFamily = "Arial, sans-serif";
    let currentFontWeight = "normal";
    let currentFontSize = "16px";
    let currentLogoUrl = null;
    let menuItems = [];

    // Debug flag to track if values have been loaded from the database
    let fontSettingsLoaded = false;

    // Helper function to parse RGB/RGBA values
    function parseRgbColor(rgbStr) {
      // Extract numbers from rgb(r, g, b) or rgba(r, g, b, a)
      const matches = rgbStr.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
      if (matches) {
        return {
          r: parseInt(matches[1]),
          g: parseInt(matches[2]),
          b: parseInt(matches[3]),
          a: matches[4] ? parseFloat(matches[4]) : 1
        };
      }
      return null;
    }

    // Helper function to detect color format
    function detectColorFormat(color) {
      if (color.startsWith('#')) {
        return 'hex';
      } else if (color.startsWith('rgba')) {
        return 'rgba';
      } else if (color.startsWith('rgb')) {
        return 'rgb';
      }
      return 'hex'; // Default to hex
    }

    // Fetch menu settings from API
    $.ajax({
      method: "GET",
      url: "/api/menu/get",
      success: function(result) {
        console.log("Menu settings loaded:", result);

        // Update global variables
        headerBgColor = result.backgroundColor || "#5156be";
        currentFontColor = result.fontColor || "#ffffff";

        // Handle font family settings
        if (result.font) {
          // Update font family if available
          if (result.font.fontFamily) {
            currentFontFamily = result.font.fontFamily;
            console.log("Loaded font family from database:", currentFontFamily);
          }

          // Update font weight if available
          if (result.font.fontWeight) {
            currentFontWeight = result.font.fontWeight;
            console.log("Loaded font weight from database:", currentFontWeight);
          }

          // Update font size if available
          if (result.font.fontSize) {
            currentFontSize = result.font.fontSize;
            // Ensure font size has 'px' unit
            if (typeof currentFontSize === 'number' || !currentFontSize.includes('px')) {
              currentFontSize = currentFontSize + 'px';
            }
            console.log("Loaded font size from database:", currentFontSize);
          }

          // Set flag to indicate font settings are loaded
          fontSettingsLoaded = true;
        }

        console.log("Updated global variables - headerBgColor:", headerBgColor,
                    "currentFontColor:", currentFontColor,
                    "currentFontFamily:", currentFontFamily,
                    "currentFontWeight:", currentFontWeight,
                    "currentFontSize:", currentFontSize);

        // Set logo source
        $(".navbar-brand .logo").attr("src", result.logoUrl || defaultLogo);

        // Apply all styling to navbar
        $(".navbar").css({
          "background-color": headerBgColor,
          "font-family": currentFontFamily,
          "font-weight": currentFontWeight,
          "font-size": currentFontSize
        });

        // Apply to all navbar elements
        $(".navbar *").css({
          "font-family": currentFontFamily,
          "font-weight": currentFontWeight
        });

        // Apply slightly smaller font size to nav links for better appearance
        const linkFontSize = parseInt(currentFontSize) * 0.9 + 'px';
        $(".navbar .nav-link").css("font-size", linkFontSize);

        // Apply font color to all navbar elements with a slight delay to ensure DOM is ready
        setTimeout(function() {
          applyFontColorToNavbar(currentFontColor);
        }, 100);

        // Initialize header background color inputs based on format
        const bgColorFormat = detectColorFormat(headerBgColor);
        if (bgColorFormat === 'rgb' || bgColorFormat === 'rgba') {
          // Parse RGB/RGBA values
          const rgbValues = parseRgbColor(headerBgColor);
          if (rgbValues) {
            // Set RGB inputs
            $("#headerBgRed, #headerBgRedA").val(rgbValues.r);
            $("#headerBgGreen, #headerBgGreenA").val(rgbValues.g);
            $("#headerBgBlue, #headerBgBlueA").val(rgbValues.b);

            // Set Alpha input if RGBA
            if (bgColorFormat === 'rgba') {
              $("#headerBgAlpha").val(rgbValues.a);
            }

            // Convert to hex for color picker
            const hexColor = rgbToHex(rgbValues.r, rgbValues.g, rgbValues.b);
            $("#headerBgColorPicker").val(hexColor);

            // Select the appropriate radio button
            $(`#headerBg${bgColorFormat.toUpperCase()}`).prop('checked', true);

            // Show the appropriate input group
            $("#headerBgRgbInputs, #headerBgRgbaInputs").addClass("d-none");
            if (bgColorFormat === 'rgb') {
              $("#headerBgRgbInputs").removeClass("d-none");
            } else {
              $("#headerBgRgbaInputs").removeClass("d-none");
            }
          }
        } else {
          // It's a hex color, just set the color picker
          $("#headerBgColorPicker").val(headerBgColor);
        }

        // Initialize font color inputs based on format
        const fontColorFormat = detectColorFormat(currentFontColor);
        if (fontColorFormat === 'rgb' || fontColorFormat === 'rgba') {
          // Parse RGB/RGBA values
          const rgbValues = parseRgbColor(currentFontColor);
          if (rgbValues) {
            // Set RGB inputs
            $("#fontRed, #fontRedA").val(rgbValues.r);
            $("#fontGreen, #fontGreenA").val(rgbValues.g);
            $("#fontBlue, #fontBlueA").val(rgbValues.b);

            // Set Alpha input if RGBA
            if (fontColorFormat === 'rgba') {
              $("#fontAlpha").val(rgbValues.a);
            }

            // Convert to hex for color picker
            const hexColor = rgbToHex(rgbValues.r, rgbValues.g, rgbValues.b);
            $("#fontColorPicker").val(hexColor);

            // Select the appropriate radio button
            $(`#font${fontColorFormat.toUpperCase()}`).prop('checked', true);

            // Show the appropriate input group
            $("#fontRgbInputs, #fontRgbaInputs").addClass("d-none");
            if (fontColorFormat === 'rgb') {
              $("#fontRgbInputs").removeClass("d-none");
            } else {
              $("#fontRgbaInputs").removeClass("d-none");
            }
          }
        } else {
          // It's a hex color, just set the color picker
          $("#fontColorPicker").val(currentFontColor);
        }
      },
      error: function(error) {
        console.log("Error fetching menu settings:", error);
        // Use default values if API fails
        $(".navbar-brand .logo").attr("src", defaultLogo);
        $(".navbar").attr("style", `background-color: ${headerBgColor}!important`);

        // Apply font color to all navbar elements with a slight delay to ensure DOM is ready
        setTimeout(function() {
          applyFontColorToNavbar(currentFontColor);
        }, 100);
      }
    });

    // Logo Editor Modal Logic

    // Handle radio button switching
    $('input[name="logoSource"]').on("change", function () {
      const selectedOption = $(this).val();

      if (selectedOption === "upload") {
        // Show upload option, hide URL option
        $("#uploadFileOption").show();
        $("#urlOption").hide();

        // Clear URL input
        $("#logoUrl").val("");
      } else if (selectedOption === "url") {
        // Show URL option, hide upload option
        $("#uploadFileOption").hide();
        $("#urlOption").show();

        // Clear file input
        $("#logoUpload").val("");
      }

      // Update preview
      updateLogoPreview();
    });

    // Clear URL button
    $("#clearUrlBtn").on("click", function () {
      $("#logoUrl").val("").focus();
      updateLogoPreview();
    });

    // Update logo preview based on current inputs
    function updateLogoPreview() {
      const hasFile = $("#logoUpload")[0].files.length > 0;
      const hasUrl = $("#logoUrl").val().trim() !== "";

      if (hasFile) {
        // Show file preview
        const file = $("#logoUpload")[0].files[0];
        currentLogoUrl = URL.createObjectURL(file);
        $("#logoPreview").attr("src", currentLogoUrl).show();
        $("#noLogoPreview").hide();
      } else if (hasUrl) {
        // Show URL preview
        currentLogoUrl = $("#logoUrl").val().trim();
        $("#logoPreview").attr("src", currentLogoUrl).show();
        $("#noLogoPreview").hide();
      } else {
        // No input, show default preview
        $("#logoPreview").hide();
        $("#noLogoPreview").show();
      }
    }

    // Handle file input
    $("#logoUpload").on("change", function (e) {
      // Switch to upload radio button
      $("#logoSourceUpload").prop("checked", true);

      // Show upload option, hide URL option
      $("#uploadFileOption").show();
      $("#urlOption").hide();

      // Clear URL input
      $("#logoUrl").val("");

      // Update preview
      updateLogoPreview();
    });

    // Handle URL input
    $("#logoUrl").on("input", function () {
      const url = $(this).val().trim();

      // Switch to URL radio button
      $("#logoSourceUrl").prop("checked", true);

      // Show URL option, hide upload option
      $("#urlOption").show();
      $("#uploadFileOption").hide();

      // Clear file input
      $("#logoUpload").val("");

      // Update preview
      updateLogoPreview();
    });

    // Open modal
    $("#edit-logo-btn").click(function () {
      currentLogoUrl = $(".navbar-brand .logo").attr("src");
      $("#logoPreview").attr("src", currentLogoUrl).show();
      $("#noLogoPreview").hide();

      // Clear both inputs initially
      $("#logoUrl").val("");
      $("#logoUpload").val("");

      // If current logo is a URL (starts with http), set the URL input and select URL radio
      if (currentLogoUrl.startsWith("http") || currentLogoUrl.startsWith("https")) {
        $("#logoUrl").val(currentLogoUrl);
        $("#logoSourceUrl").prop("checked", true);
        $("#urlOption").show();
        $("#uploadFileOption").hide();
      } else {
        // Otherwise, select the upload radio
        $("#logoSourceUpload").prop("checked", true);
        $("#uploadFileOption").show();
        $("#urlOption").hide();
      }

      // Show the modal
      $("#logoEditorModal").modal("show");
    });

    // Save logo
    $("#save-logo").click(async function () {
      const fileInput = $("#logoUpload")[0];
      const formData = new FormData();

      if (fileInput.files.length > 0) {
        // Add file to form data
        formData.append('logo', fileInput.files[0]);

        // Show loading state
        $("#save-logo").prop("disabled", true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Saving...');

        // Upload file
        $.ajax({
          method: "POST",
          url: "/api/menu/save",
          data: formData,
          processData: false,
          contentType: false,
          success: function(result) {
            console.log(result);
            $(".navbar-brand .logo").attr("src", result.logoUrl);
            URL.revokeObjectURL(currentLogoUrl); // Clean up memory

            const logoModal = bootstrap.Modal.getInstance(
              document.getElementById("logoEditorModal")
            );
            if (logoModal) {
              logoModal.hide();
            }

            // Reset button state
            $("#save-logo").prop("disabled", false).html('<i class="fas fa-save me-1"></i> Save Changes');

            showToast("Logo saved successfully");
          },
          error: function(error) {
            console.log(error);
            showToast("Error saving logo", "danger");

            // Reset button state
            $("#save-logo").prop("disabled", false).html('<i class="fas fa-save me-1"></i> Save Changes');
          }
        });
      } else if ($("#logoUrl").val().trim()) {
        // Save URL
        $.ajax({
          method: "POST",
          url: "/api/menu/save",
          contentType: "application/json",
          data: JSON.stringify({
            logoUrl: $("#logoUrl").val().trim()
          }),
          success: function(result) {
            console.log(result);
            $(".navbar-brand .logo").attr("src", result.logoUrl);

            const logoModal = bootstrap.Modal.getInstance(
              document.getElementById("logoEditorModal")
            );
            if (logoModal) {
              logoModal.hide();
            }

            showToast("Logo saved successfully");
          },
          error: function(error) {
            console.log(error);
            showToast("Error saving logo", "danger");
          }
        });
      } else {
        showToast("Please select a file or enter a URL", "warning");
        return;
      }
    });

    //  close modal - this is redundant now as we have a global handler
    // but keeping it for completeness
    $("#close-logo-modal").on("click", function () {
      const logoModal = bootstrap.Modal.getInstance(
        document.getElementById("logoEditorModal")
      );
      if (logoModal) {
        logoModal.hide();
      }
    });

    // Header Background Color Functionality

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
      // Handle non-hex colors
      if (!hex || !hex.startsWith('#')) {
        // Try to extract RGB values if it's an rgb/rgba color
        const rgbValues = parseRgbColor(hex);
        if (rgbValues) {
          return { r: rgbValues.r, g: rgbValues.g, b: rgbValues.b };
        }

        // Default values if we can't parse the color
        return { r: 0, g: 0, b: 0 };
      }

      // Remove the # if present
      hex = hex.replace("#", "");

      // Handle shorthand hex (e.g., #FFF)
      if (hex.length === 3) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
      }

      // Parse the hex values
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return { r, g, b };
    }

    // Helper function to convert RGB to hex
    function rgbToHex(r, g, b) {
      return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    //     const rgbToHex = (r, g, b) =>
    // "#" + [r, g, b].map(x => x.toString(16).padStart(2, '0')).join('');

    // Initialize color picker with stored value

    // Open header background color modal
    $("#edit-header-bg-btn").click(function () {
      // Update the header background preview text color with the current font color
      $("#headerBgPreview span").css("color", currentFontColor);

      // Set the text input and preview with the current background color
      $("#headerBgColorText").val(headerBgColor);
      $("#headerBgPreview").css("background-color", headerBgColor);

      // Initialize inputs based on color format
      const bgColorFormat = detectColorFormat(headerBgColor);

      // Select the appropriate radio button
      $(`#headerBg${bgColorFormat.toUpperCase()}`).prop('checked', true);

      // Show the appropriate input group
      $("#headerBgRgbInputs, #headerBgRgbaInputs").addClass("d-none");
      if (bgColorFormat === 'rgb') {
        $("#headerBgRgbInputs").removeClass("d-none");
      } else if (bgColorFormat === 'rgba') {
        $("#headerBgRgbaInputs").removeClass("d-none");
      }

      if (bgColorFormat === 'rgb' || bgColorFormat === 'rgba') {
        // Parse RGB/RGBA values
        const rgbValues = parseRgbColor(headerBgColor);
        if (rgbValues) {
          // Set RGB inputs
          $("#headerBgRed, #headerBgRedA").val(rgbValues.r);
          $("#headerBgGreen, #headerBgGreenA").val(rgbValues.g);
          $("#headerBgBlue, #headerBgBlueA").val(rgbValues.b);

          // Set Alpha input if RGBA
          if (bgColorFormat === 'rgba') {
            $("#headerBgAlpha").val(rgbValues.a);
          }

          // Convert to hex for color picker
          const hexColor = rgbToHex(rgbValues.r, rgbValues.g, rgbValues.b);
          $("#headerBgColorPicker").val(hexColor);
        }
      } else {
        // It's a hex color, just set the color picker
        $("#headerBgColorPicker").val(headerBgColor);
      }

      $("#headerBgColorModal").modal("show");
    });

    // Handle color format selection
    $("input[name='headerBgColorFormat']").change(function () {
      const format = $(this).val();

      // Hide all input groups first
      $("#headerBgRgbInputs, #headerBgRgbaInputs").addClass("d-none");

      // Show the selected input group
      if (format === "rgb") {
        $("#headerBgRgbInputs").removeClass("d-none");
      } else if (format === "rgba") {
        $("#headerBgRgbaInputs").removeClass("d-none");
      }

      // Update the text input based on the selected format
      updateHeaderBgTextFromFormat();
    });

    // Function to update the text input based on the selected format
    function updateHeaderBgTextFromFormat() {
      const format = $("input[name='headerBgColorFormat']:checked").val();
      let colorText = "";

      if (format === "hex") {
        colorText = $("#headerBgColorPicker").val();
      } else if (format === "rgb") {
        const r = $("#headerBgRed").val();
        const g = $("#headerBgGreen").val();
        const b = $("#headerBgBlue").val();
        colorText = `rgb(${r}, ${g}, ${b})`;
      } else if (format === "rgba") {
        const r = $("#headerBgRedA").val();
        const g = $("#headerBgGreenA").val();
        const b = $("#headerBgBlueA").val();
        const a = $("#headerBgAlpha").val();
        colorText = `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      $("#headerBgColorText").val(colorText);
      $("#headerBgPreview").css("background-color", colorText);

      // Make sure the preview text is always using the current font color
    }

    // Update preview when color picker changes
    $("#headerBgColorPicker").on("input", function () {
      const color = $(this).val();

      // Update RGB inputs
      const rgb = hexToRgb(color);
      $("#headerBgRed, #headerBgRedA").val(rgb.r);
      $("#headerBgGreen, #headerBgGreenA").val(rgb.g);
      $("#headerBgBlue, #headerBgBlueA").val(rgb.b);

      // Update text input based on selected format
      updateHeaderBgTextFromFormat();
    });

    // Update preview when RGB inputs change
    $("#headerBgRed, #headerBgGreen, #headerBgBlue").on("input", function () {
      const r = $("#headerBgRed").val();
      const g = $("#headerBgGreen").val();
      const b = $("#headerBgBlue").val();

      // Update color picker
      const hex = rgbToHex(parseInt(r), parseInt(g), parseInt(b));
      $("#headerBgColorPicker").val(hex);

      // Update RGBA inputs
      $("#headerBgRedA").val(r);
      $("#headerBgGreenA").val(g);
      $("#headerBgBlueA").val(b);

      // Update text input
      updateHeaderBgTextFromFormat();
    });

    // Update preview when RGBA inputs change
    $("#headerBgRedA, #headerBgGreenA, #headerBgBlueA, #headerBgAlpha").on(
      "input",
      function () {
        const r = $("#headerBgRedA").val();
        const g = $("#headerBgGreenA").val();
        const b = $("#headerBgBlueA").val();

        // Update color picker (ignoring alpha)
        const hex = rgbToHex(parseInt(r), parseInt(g), parseInt(b));
        $("#headerBgColorPicker").val(hex);

        // Update RGB inputs
        $("#headerBgRed").val(r);
        $("#headerBgGreen").val(g);
        $("#headerBgBlue").val(b);

        // Update text input
        updateHeaderBgTextFromFormat();
      }
    );

    // Update preview when text input changes
    $("#headerBgColorText").on("input", function () {
      const color = $(this).val();
      try {
        // Check if it's a valid color
        $("<div>").css("background-color", color);
        $("#headerBgPreview").css("background-color", color);

        // Try to update other inputs if it's a hex color
        if (
          color.startsWith("#") &&
          (color.length === 7 || color.length === 4)
        ) {
          $("#headerBgColorPicker").val(color);

          // Update RGB inputs
          const rgb = hexToRgb(color);
          $("#headerBgRed, #headerBgRedA").val(rgb.r);
          $("#headerBgGreen, #headerBgGreenA").val(rgb.g);
          $("#headerBgBlue, #headerBgBlueA").val(rgb.b);
        }
      } catch (e) {
        // Invalid color, don't update preview
      }
    });

    // Handle preset color clicks
    $(".color-preset").on("click", function () {
      const color = $(this).data("color");
      $("#headerBgColorPicker").val(color);

      // Update RGB inputs
      const rgb = hexToRgb(color);
      $("#headerBgRed, #headerBgRedA").val(rgb.r);
      $("#headerBgGreen, #headerBgGreenA").val(rgb.g);
      $("#headerBgBlue, #headerBgBlueA").val(rgb.b);

      // Update text input based on selected format
      updateHeaderBgTextFromFormat();
    });

    // Save header background color
    $("#save-header-bg").click(function () {
      const format = $("input[name='headerBgColorFormat']:checked").val();
      let color;

      if (format === "hex") {
        color = $("#headerBgColorPicker").val();
      } else if (format === "rgb") {
        const r = $("#headerBgRed").val();
        const g = $("#headerBgGreen").val();
        const b = $("#headerBgBlue").val();
        color = `rgb(${r}, ${g}, ${b})`;
      } else if (format === "rgba") {
        const r = $("#headerBgRedA").val();
        const g = $("#headerBgGreenA").val();
        const b = $("#headerBgBlueA").val();
        const a = $("#headerBgAlpha").val();
        color = `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      const $saveBtn = $(this);
      $saveBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Saving...');
      $.ajax({
        method: "POST",
        url: "/api/menu/save",
        contentType: "application/json",
        data: JSON.stringify({
          items: [],
          backgroundColor: color,
        }),
        success: function (result) {
          console.log(result);
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');

          // Update the global variable
          headerBgColor = result.backgroundColor;

          // Update the navbar background color
          $(".navbar").attr(
            "style",
            `background-color: ${headerBgColor}!important`
          );

          // Also update the font color preview background if it's visible
          $("#fontColorPreview").css("background-color", headerBgColor);

          $("#headerBgColorModal").modal("hide");
          showToast("Header background color saved successfully");
        },
        error: function (error) {
          console.log(error);
          showToast("Error adding menu item", "danger");
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');
        },
      });
    });

    // Open font color modal
    $("#edit-font-color-btn").click(function () {
      // Update the font color preview background to match the current header background color
      $("#fontColorPreview").css("background-color", headerBgColor);

      // Set the text input and preview with the current font color
      $("#fontColorText").val(currentFontColor);
      $("#fontColorPreview span").css("color", currentFontColor);

      // Initialize inputs based on color format
      const fontColorFormat = detectColorFormat(currentFontColor);

      // Select the appropriate radio button
      $(`#font${fontColorFormat.toUpperCase()}`).prop('checked', true);

      // Show the appropriate input group
      $("#fontRgbInputs, #fontRgbaInputs").addClass("d-none");
      if (fontColorFormat === 'rgb') {
        $("#fontRgbInputs").removeClass("d-none");
      } else if (fontColorFormat === 'rgba') {
        $("#fontRgbaInputs").removeClass("d-none");
      }

      if (fontColorFormat === 'rgb' || fontColorFormat === 'rgba') {
        // Parse RGB/RGBA values
        const rgbValues = parseRgbColor(currentFontColor);
        if (rgbValues) {
          // Set RGB inputs
          $("#fontRed, #fontRedA").val(rgbValues.r);
          $("#fontGreen, #fontGreenA").val(rgbValues.g);
          $("#fontBlue, #fontBlueA").val(rgbValues.b);

          // Set Alpha input if RGBA
          if (fontColorFormat === 'rgba') {
            $("#fontAlpha").val(rgbValues.a);
          }

          // Convert to hex for color picker
          const hexColor = rgbToHex(rgbValues.r, rgbValues.g, rgbValues.b);
          $("#fontColorPicker").val(hexColor);
        }
      } else {
        // It's a hex color, just set the color picker
        $("#fontColorPicker").val(currentFontColor);
      }

      $("#fontColorModal").modal("show");
    });

    // Handle color format selection for font
    $("input[name='fontColorFormat']").change(function () {
      const format = $(this).val();

      // Hide all input groups first
      $("#fontRgbInputs, #fontRgbaInputs").addClass("d-none");

      // Show the selected input group
      if (format === "rgb") {
        $("#fontRgbInputs").removeClass("d-none");
      } else if (format === "rgba") {
        $("#fontRgbaInputs").removeClass("d-none");
      }

      // Update the text input based on the selected format
      updateFontColorTextFromFormat();
    });

    // Function to update the text input based on the selected format
    function updateFontColorTextFromFormat() {
      const format = $("input[name='fontColorFormat']:checked").val();
      let colorText = "";

      if (format === "hex") {
        colorText = $("#fontColorPicker").val();
      } else if (format === "rgb") {
        const r = $("#fontRed").val();
        const g = $("#fontGreen").val();
        const b = $("#fontBlue").val();
        colorText = `rgb(${r}, ${g}, ${b})`;
      } else if (format === "rgba") {
        const r = $("#fontRedA").val();
        const g = $("#fontGreenA").val();
        const b = $("#fontBlueA").val();
        const a = $("#fontAlpha").val();
        colorText = `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      $("#fontColorText").val(colorText);
      $("#fontColorPreview span").css("color", colorText);

      // Make sure the preview background is always using the current header background color
      $("#fontColorPreview").css("background-color", headerBgColor);
    }

    // Update preview when color picker changes
    $("#fontColorPicker").on("input", function () {
      const color = $(this).val();

      // Update RGB inputs
      const rgb = hexToRgb(color);
      $("#fontRed, #fontRedA").val(rgb.r);
      $("#fontGreen, #fontGreenA").val(rgb.g);
      $("#fontBlue, #fontBlueA").val(rgb.b);

      // Update text input based on selected format
      updateFontColorTextFromFormat();
    });

    // Update preview when RGB inputs change
    $("#fontRed, #fontGreen, #fontBlue").on("input", function () {
      const r = $("#fontRed").val();
      const g = $("#fontGreen").val();
      const b = $("#fontBlue").val();

      // Update color picker
      const hex = rgbToHex(parseInt(r), parseInt(g), parseInt(b));
      $("#fontColorPicker").val(hex);

      // Update RGBA inputs
      $("#fontRedA").val(r);
      $("#fontGreenA").val(g);
      $("#fontBlueA").val(b);

      // Update text input
      updateFontColorTextFromFormat();
    });

    // Update preview when RGBA inputs change
    $("#fontRedA, #fontGreenA, #fontBlueA, #fontAlpha").on(
      "input",
      function () {
        const r = $("#fontRedA").val();
        const g = $("#fontGreenA").val();
        const b = $("#fontBlueA").val();

        // Update color picker (ignoring alpha)
        const hex = rgbToHex(parseInt(r), parseInt(g), parseInt(b));
        $("#fontColorPicker").val(hex);

        // Update RGB inputs
        $("#fontRed").val(r);
        $("#fontGreen").val(g);
        $("#fontBlue").val(b);

        // Update text input
        updateFontColorTextFromFormat();
      }
    );

    // Update preview when text input changes
    $("#fontColorText").on("input", function () {
      const color = $(this).val();
      try {
        // Check if it's a valid color
        $("<div>").css("color", color);
        $("#fontColorPreview span").css("color", color);

        // Try to update other inputs if it's a hex color
        if (
          color.startsWith("#") &&
          (color.length === 7 || color.length === 4)
        ) {
          $("#fontColorPicker").val(color);

          // Update RGB inputs
          const rgb = hexToRgb(color);
          $("#fontRed, #fontRedA").val(rgb.r);
          $("#fontGreen, #fontGreenA").val(rgb.g);
          $("#fontBlue, #fontBlueA").val(rgb.b);
        }
      } catch (e) {
        // Invalid color, don't update preview
      }
    });

    // Handle preset color clicks for font
    $(".color-preset-font").on("click", function () {
      const color = $(this).data("color");
      $("#fontColorPicker").val(color);

      // Update RGB inputs
      const rgb = hexToRgb(color);
      $("#fontRed, #fontRedA").val(rgb.r);
      $("#fontGreen, #fontGreenA").val(rgb.g);
      $("#fontBlue, #fontBlueA").val(rgb.b);

      // Update text input based on selected format
      updateFontColorTextFromFormat();
    });

    // Save font color
    $("#save-font-color").click(function () {
      // Disable the save button to prevent multiple clicks
      const $saveBtn = $(this);
      $saveBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Saving...');

      const format = $("input[name='fontColorFormat']:checked").val();
      let color;

      if (format === "hex") {
        color = $("#fontColorPicker").val();
      } else if (format === "rgb") {
        const r = $("#fontRed").val();
        const g = $("#fontGreen").val();
        const b = $("#fontBlue").val();
        color = `rgb(${r}, ${g}, ${b})`;
      } else if (format === "rgba") {
        const r = $("#fontRedA").val();
        const g = $("#fontGreenA").val();
        const b = $("#fontBlueA").val();
        const a = $("#fontAlpha").val();
        color = `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      console.log("Saving font color:", color);

      // First, apply changes immediately to the navbar for real-time feedback
      currentFontColor = color;

      // Apply to all navbar elements immediately
      try {
        applyFontColorToNavbar(color);

        // Also update preview elements
        $("#headerBgPreview span").css("color", color);
        $("#fontFamilyPreview span").css("color", color);

        console.log("Font color applied immediately:", color);
      } catch (error) {
        console.error("Error applying font color immediately:", error);
      }

      // Then save to the server
      $.ajax({
        method: "POST",
        url: "/api/menu/save",
        contentType: "application/json",
        data: JSON.stringify({
          items: [],
          fontColor: color,
        }),
        success: function (result) {
          console.log("Font color saved successfully:", result);

          // Update the global variable with server response (in case server modified the value)
          if (result.fontColor) {
            currentFontColor = result.fontColor;
            console.log("Font color updated from server:", currentFontColor);

            // Apply the final value from server if it differs
            if (result.fontColor !== color) {
              applyFontColorToNavbar(currentFontColor);

              // Also update previews
              $("#headerBgPreview span").css("color", currentFontColor);
              $("#fontFamilyPreview span").css("color", currentFontColor);
            }
          }

          // Close the modal
          $("#fontColorModal").modal("hide");

          // Show success message
          showToast("Font color saved successfully");

          // Reset button state
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');
        },
        error: function (error) {
          console.log("Error saving font color:", error);
          showToast("Error saving font color", "danger");

          // Reset button state
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');
        },
      });
    });

    // Font Family Modal Functionality

    // Open font family modal
    $("#edit-font-family-btn").click(function () {
      try {
        console.log("Opening font family modal");

        // Ensure the modal is properly initialized
        if (!bootstrap.Modal.getInstance(document.getElementById("fontFamilyModal"))) {
          new bootstrap.Modal(document.getElementById("fontFamilyModal"));
        }

        // Fetch current font settings from the server
        $.ajax({
          method: "GET",
          url: "/api/menu/get",
          success: function(result) {
            console.log("Font settings fetched for modal:", result);

            // Update the values if they exist in the response
            if (result.font && result.font.fontFamily) {
              currentFontFamily = result.font.fontFamily;
            }

            if (result.font && result.font.fontWeight) {
              currentFontWeight = result.font.fontWeight;
            }

            if (result.font && result.font.fontSize) {
              currentFontSize = result.font.fontSize;
              // Ensure font size has 'px' unit
              if (typeof currentFontSize === 'number' || !currentFontSize.includes('px')) {
                currentFontSize = currentFontSize + 'px';
              }
            }

            // Now populate the modal with these values
            populateFontFamilyModal();
          },
          error: function(error) {
            console.error("Error fetching font settings for modal:", error);
            // Still try to populate with default values
            populateFontFamilyModal();
          }
        });

        // Function to populate the font family modal
        function populateFontFamilyModal() {
          try {
            console.log("Populating font family modal with:", {
              fontFamily: currentFontFamily,
              fontWeight: currentFontWeight,
              fontSize: currentFontSize
            });

            // Set the font family dropdown
            if ($("#fontFamilySelect option[value=\"" + currentFontFamily + "\"]").length > 0) {
              $("#fontFamilySelect").val(currentFontFamily);
            } else {
              // If exact match not found, use default
              $("#fontFamilySelect").val("Arial, sans-serif");
            }

            // Set the font weight dropdown
            if ($("#fontWeightSelect option[value=\"" + currentFontWeight + "\"]").length > 0) {
              $("#fontWeightSelect").val(currentFontWeight);
            } else {
              // Default to normal if not found
              $("#fontWeightSelect").val("normal");
            }

            // Set the font size input
            let fontSize = currentFontSize;
            if (typeof fontSize === 'string' && fontSize.endsWith('px')) {
              fontSize = parseInt(fontSize.replace('px', ''));
            } else if (typeof fontSize === 'string') {
              fontSize = parseInt(fontSize) || 16; // Default to 16 if parsing fails
            } else if (typeof fontSize === 'number') {
              // It's already a number
            } else {
              fontSize = 16; // Default if we can't determine the type
            }

            $("#fontSizeInput").val(fontSize);

            // Update the preview with current values
            $("#fontFamilyPreview").css({
              "font-family": currentFontFamily,
              "font-weight": currentFontWeight,
              "font-size": currentFontSize,
              "background-color": headerBgColor
            });

            // Make sure the preview text is using the current font color
            $("#fontFamilyPreview span").css("color", currentFontColor);

            console.log("Font family modal populated successfully");
          } catch (error) {
            console.error("Error setting values in font family modal:", error);
          }
        }

        // Show the modal
        $("#fontFamilyModal").modal("show");
      } catch (error) {
        console.error("Error opening font family modal:", error);
        alert("Could not open the font settings. Please try refreshing the page.");
      }
    });

    // Open favicon modal
    $("#edit-favicon-btn").click(function () {
      try {
        console.log("Opening favicon modal");

        // Load current favicon if exists
        loadCurrentFavicon();

        // Show the modal
        $("#faviconModal").modal("show");
      } catch (error) {
        console.error("Error opening favicon modal:", error);
        showToast("Could not open favicon settings. Please try refreshing the page.", "danger");
      }
    });

    // Function to load current favicon
    function loadCurrentFavicon() {
      // Check if there's a current favicon in the page
      const currentFavicon = $('link[rel="icon"], link[rel="shortcut icon"]').attr('href');
      if (currentFavicon) {
        $("#faviconUrlInput").val(currentFavicon);
        showFaviconPreview(currentFavicon);
        $("#save-favicon").prop('disabled', false);
      }
    }

    // Handle file input change for favicon
    $("#faviconFileInput").on('change', function() {
      const file = this.files[0];

      if (!file) {
        // Clear preview if no file selected
        $("#faviconPreview").hide();
        $("#faviconPlaceholder").show().html('<i class="fas fa-star" style="font-size: 32px; color: #dee2e6;"></i><div class="mt-2 small">No favicon selected</div>');
        $("#save-favicon").prop('disabled', true);
        return;
      }

      // Validate file type
      const allowedTypes = ['image/x-icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        showToast("Please select a valid favicon file (ICO, PNG, JPG, GIF, SVG)", "danger");
        // Clear the input
        $(this).val('');
        $("#faviconPreview").hide();
        $("#faviconPlaceholder").show().html('<i class="fas fa-exclamation-triangle text-warning" style="font-size: 32px;"></i><div class="mt-2 small text-warning">Invalid file type</div>');
        $("#save-favicon").prop('disabled', true);
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        showToast("File size must be less than 2MB", "danger");
        // Clear the input
        $(this).val('');
        $("#faviconPreview").hide();
        $("#faviconPlaceholder").show().html('<i class="fas fa-exclamation-triangle text-warning" style="font-size: 32px;"></i><div class="mt-2 small text-warning">File too large</div>');
        $("#save-favicon").prop('disabled', true);
        return;
      }

      // Create preview URL for the file
      const previewUrl = URL.createObjectURL(file);
      showFaviconPreview(previewUrl);
      $("#save-favicon").prop('disabled', false);

      // Clear URL input since we're using uploaded file
      $("#faviconUrlInput").val('');
    });

    // Handle URL input change for favicon
    $("#faviconUrlInput").on('input blur', function() {
      const url = $(this).val().trim();

      if (!url) {
        // Clear preview if no URL entered
        $("#faviconPreview").hide();
        $("#faviconPlaceholder").show().html('<i class="fas fa-star" style="font-size: 32px; color: #dee2e6;"></i><div class="mt-2 small">No favicon selected</div>');
        $("#save-favicon").prop('disabled', true);
        return;
      }

      // Validate URL format
      try {
        new URL(url);
      } catch (e) {
        $("#faviconPreview").hide();
        $("#faviconPlaceholder").show().html('<i class="fas fa-exclamation-triangle text-warning" style="font-size: 32px;"></i><div class="mt-2 small text-warning">Invalid URL format</div>');
        $("#save-favicon").prop('disabled', true);
        return;
      }

      // Show preview and enable save button
      showFaviconPreview(url);
      $("#save-favicon").prop('disabled', false);

      // Clear file input since we're using URL
      $("#faviconFileInput").val('');
    });

    // Function to show favicon preview
    function showFaviconPreview(url) {
      const $preview = $("#faviconPreview");
      const $placeholder = $("#faviconPlaceholder");

      $preview.attr('src', url).show();
      $placeholder.hide();

      // Handle image load error
      $preview.off('error').on('error', function() {
        $preview.hide();
        $placeholder.show();
        $placeholder.html('<i class="fas fa-exclamation-triangle text-warning" style="font-size: 32px;"></i><div class="mt-2 small text-warning">Failed to load favicon</div>');
        $("#save-favicon").prop('disabled', true);
        showToast("Failed to load favicon from URL", "danger");
      });
    }

    // Save favicon
    $("#save-favicon").click(function() {
      const $saveBtn = $(this);
      $saveBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span> Saving...');

      const faviconUrl = $("#faviconUrlInput").val().trim();
      const fileInput = $("#faviconFileInput")[0];

      let requestData;
      let isFileUpload = false;

      if (fileInput.files.length > 0) {
        // File upload
        requestData = new FormData();
        requestData.append('favIcon', fileInput.files[0]);
        isFileUpload = true;
      } else if (faviconUrl) {
        // URL
        requestData = JSON.stringify({ faviconUrl: faviconUrl });
        isFileUpload = false;
      } else {
        showToast("Please select a file or enter a URL", "warning");
        $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Favicon');
        return;
      }

      $.ajax({
        url: '/api/menu/save',
        method: 'POST',
        data: requestData,
        processData: !isFileUpload,
        contentType: isFileUpload ? false : 'application/json',
        success: function(response) {
          console.log("Favicon saved successfully:", response);

          // Update the page favicon
          if (response.favIcon) {
            updatePageFavicon(response.favIcon);
          } else if (faviconUrl) {
            updatePageFavicon(faviconUrl);
          }

          // Close modal and show success
          $("#faviconModal").modal("hide");
          showToast("Favicon saved successfully", "success");

          // Reset button
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Favicon');

          // Clean up any object URLs to prevent memory leaks
          const previewSrc = $("#faviconPreview").attr('src');
          if (previewSrc && previewSrc.startsWith('blob:')) {
            URL.revokeObjectURL(previewSrc);
          }
        },
        error: function(xhr, status, error) {
          console.error("Error saving favicon:", error);
          showToast("Failed to save favicon. Please try again.", "danger");

          // Reset button
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Favicon');
        }
      });
    });



    // Clear favicon inputs when modal is hidden
    $("#faviconModal").on('hidden.bs.modal', function() {
      $("#faviconFileInput").val('');
      $("#faviconUrlInput").val('');
      $("#faviconPreview").hide();
      $("#faviconPlaceholder").show().html('<i class="fas fa-star" style="font-size: 32px; color: #dee2e6;"></i><div class="mt-2 small">No favicon selected</div>');
      $("#save-favicon").prop('disabled', true);

      // Clean up any object URLs to prevent memory leaks
      const previewSrc = $("#faviconPreview").attr('src');
      if (previewSrc && previewSrc.startsWith('blob:')) {
        URL.revokeObjectURL(previewSrc);
      }
    });

    // Update preview when font family changes
    $("#fontFamilySelect, #fontWeightSelect, #fontSizeInput").on("change input", function() {
      updateFontFamilyPreview();
    });

    // Function to update the font family preview
    function updateFontFamilyPreview() {
      try {
        const fontFamily = $("#fontFamilySelect").val();
        const fontWeight = $("#fontWeightSelect").val();
        const fontSize = $("#fontSizeInput").val() + "px";

        $("#fontFamilyPreview").css({
          "font-family": fontFamily,
          "font-weight": fontWeight,
          "font-size": fontSize,
          "background-color": headerBgColor
        });

        // Make sure the preview text is using the current font color
        $("#fontFamilyPreview span").css("color", currentFontColor);

        console.log("Font family preview updated:", {
          fontFamily: fontFamily,
          fontWeight: fontWeight,
          fontSize: fontSize
        });
      } catch (error) {
        console.error("Error updating font family preview:", error);
      }
    }

    // Save font family settings
    $("#save-font-family").click(function() {
      // Disable the save button to prevent multiple clicks
      const $saveBtn = $(this);
      $saveBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Saving...');

      const fontFamily = $("#fontFamilySelect").val();
      const fontWeight = $("#fontWeightSelect").val();
      const fontSize = $("#fontSizeInput").val() + "px";

      console.log("Saving font settings:", {
        fontFamily: fontFamily,
        fontWeight: fontWeight,
        fontSize: fontSize
      });

      // First, apply changes immediately to the navbar for real-time feedback
      currentFontFamily = fontFamily;
      currentFontWeight = fontWeight;
      currentFontSize = fontSize;

      // Apply the changes to the navbar immediately
      $(".navbar").css({
        "font-family": fontFamily,
        "font-weight": fontWeight,
        "font-size": fontSize
      });

      // Apply to all navbar elements
      $(".navbar *").css({
        "font-family": fontFamily,
        "font-weight": fontWeight
      });

      // Apply slightly smaller font size to nav links for better appearance
      const linkFontSize = parseInt(fontSize) * 0.9 + 'px';
      $(".navbar .nav-link").css("font-size", linkFontSize);

      // Then save to the server
      $.ajax({
        method: "POST",
        url: "/api/menu/save",
        contentType: "application/json",
        data: JSON.stringify({
          fontFamily: fontFamily,
          fontWeight: fontWeight,
          fontSize: fontSize,
          items: []
        }),
        success: function(result) {
          console.log("Font settings saved successfully:", result);

          // Close the modal
          $("#fontFamilyModal").modal("hide");

          // Show success message
          showToast("Font settings saved successfully");

          // Reset button state
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');
        },
        error: function(error) {
          console.log("Error saving font settings:", error);
          showToast("Error saving font settings", "danger");

          // Reset button state
          $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i> Save Changes');
        }
      });
    });
  });
</script>
