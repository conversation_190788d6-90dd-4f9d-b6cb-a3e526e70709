const mongoose = require("mongoose");

// Sub-schemas
const BackupSecuritySchema = new mongoose.Schema({
  pointOfContact: { type: String, trim: true, default: "" },
  superadminEmail: { type: String, trim: true },
  emergencyContactNo: { type: String, trim: true },
  emergencyEmail: { type: String, trim: true },
  knownIPs: [{ type: String, trim: true }],
});

const SupportSchema = new mongoose.Schema({
  accountManagerName: { type: String, trim: true },
  accountManagerEmail: { type: String, trim: true },
  accountManagerPhone: { type: String, trim: true },
});

const FinancialInformationSchema = new mongoose.Schema({
  paymentInformation: { type: String, trim: true },
  paymentTerms: { type: String, trim: true },
  currency: { type: String, trim: true },
  timezone: { type: String, trim: true },
  invoiceNumberingSystem: { type: String, trim: true },
});

// Main schema
const businessSchema = new mongoose.Schema({
  businessName: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  businessLogo: { type: String, trim: true },
  businessEmail: { type: String, trim: true },
  businessPhone: { type: String, trim: true },
  businessWebsite: { type: String, trim: true },
  businessAddress1: { type: String, trim: true },
  businessAddress2: { type: String, trim: true },
  businessStructure: { type: String, trim: true },
  industry: { type: String, trim: true },
  products: [{ type: String, trim: true }],
  services: [{ type: String, trim: true }],

  // Business address
  businessAddress: { type: String, trim: true },
  country: { type: String, trim: true },
  state: { type: String, trim: true },
  city: { type: String, trim: true },
  postalCode: { type: String, trim: true },
  region: { type: String, trim: true },
  currency: { type: String, trim: true },
  businessType: { type: String, trim: true },

  // Billing address
  billingOfficeAddress: { type: String, trim: true },
  billingCountry: { type: String, trim: true },
  billingState: { type: String, trim: true },
  billingZip: { type: String, trim: true },

  // Legal and tax information
  vatGstNumber: { type: String, trim: true },
  businessRegistrationNumber: { type: String, trim: true },
  taxIdentificationNumber: { type: String, trim: true },
  businessContactInformation: { type: String, trim: true },

  // Backup & security
  backupSchedule: { type: String, trim: true },
  backupLocation: { type: String, trim: true },
  backupEmail: { type: String, trim: true },

  // Timestamps and creator info
  createdDate: { type: Date, default: Date.now, immutable: true },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    immutable: true,
  },
  createdIP: { type: String, immutable: true },
  createdCountry: { type: String, immutable: true },
  createdEmail: { type: String, immutable: true },
  createdPhone: { type: String, immutable: true },

  // Sub-schemas
  backupSecurity: BackupSecuritySchema,
  support: SupportSchema,
  financialInformation: FinancialInformationSchema,

  termsAndConditions: { type: String, trim: true },
  notification_type: {
    type: [String],
  },
  updates_on: {
    type: [String],
  },

  // Header and Footer Scripts for public pages
  headerScripts: { type: String, trim: true, default: "" },
  footerScripts: { type: String, trim: true, default: "" },
  socialMedia: {
    facebook: {
      type: String,
      trim: true,
    },
    twitter: {
      type: String,
      trim: true,
    },
    linkedin: {
      type: String,
      trim: true,
    },
    instagram: {
      type: String,
      trim: true,
    },
    youtube: {
      type: String,
      trim: true,
    },
    pinterest: {
      type: String,
      trim: true,
    },

    github: {
      type: String,
      trim: true,
    },
    discord: {
      type: String,
      trim: true,
    },
    medium: {
      type: String,
      trim: true,
    },
    reddit: {
      type: String,
      trim: true,
    },
  },
});

//, validate: [validatePhone, 'Invalid phone number']

//, validate: [validateEmail, 'Invalid email']
// Helper function for validating emails
function validateEmail(email) {
  const re = /^\+?[0-9]{10,15}$/;
  return re.test(email);
}

// Helper function for validating phone numbers (basic example)
function validatePhone(phone) {
  const re = /^[0-9]{10,15}$/;
  return re.test(phone);
}

module.exports = mongoose.model("Business", businessSchema);
