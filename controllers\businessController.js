const Business = require("../models/Business");
const maxmind = require("maxmind");
const path = require("path");

// Define the path to the GeoLite2-City.mmdb file
const dbPath = path.join(__dirname, "../data/GeoLite2-City.mmdb");

// Initialize MaxMind database reader
let geoipReader;

const initGeoIPReader = async () => {
  geoipReader = await maxmind.open(dbPath);
};

initGeoIPReader(); // Initialize the MaxMind reader once when the server starts

// Create a new business record
exports.createBusiness = async (req, res) => {
  try {
    const { createdIP, createdEmail } = req.body;
    const createdBy = req.user._id; // Use req.user._id for createdBy

    // Fetch country information from MaxMind database
    const location = geoipReader.get(createdIP);
    const createdCountry = location ? location.country.iso_code : "Unknown"; // Adjust based on actual data

    // Create the business record
    const business = new Business({
      ...req.body,
      createdBy: req.user._id,
      createdIP: req.ip,
      createdCountry,
      createdEmail: req.user.email,
    });

    await business.save();
    res.status(201).json(business);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Create a new business record
exports.createBusinessNoMetadata = async (req, res) => {
  try {
    const business = new Business(req.body);
    await business.save();
    res.status(201).json(business);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get all business records
exports.getAllBusinesses = async (req, res) => {
  try {
    const businesses = await Business.find();
    res.status(200).json(businesses);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get a single business record by ID
exports.getBusinessById = async (req, res) => {
  try {
    const business = await Business.findById(req.params.id);
    if (!business) {
      return res.status(404).json({ error: "Business not found" });
    }
    res.status(200).json(business);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update a business record by ID
exports.updateBusinessById = async (req, res) => {
  try {
    const business = await Business.findByIdAndUpdate(
      req.params.id,
      { $set: req.body },
      { new: true, runValidators: true }
    );
    if (!business) {
      console.log("error updaing busness:", error);
      return res.status(404).json({ error: "Business not found" });
    }
    res.status(200).json(business);
  } catch (error) {
    console.log("error updaing busness:", error);

    res.status(400).json({ error: error.message });
  }
};
exports.updateBusinessNotificationById = async (req, res) => {
  try {
    const { notification_type = [], updates_on = [] } = req.body;

    const business = await Business.findByIdAndUpdate(
      req.params.id,
      { $set: { notification_type, updates_on } },
      { new: true, runValidators: true }
    );
    if (!business) {
      console.log("error updaing busness:", error);
      return res.status(404).json({ error: "Business not found" });
    }
    res.status(200).json(business);
  } catch (error) {
    console.log("error updaing busness:", error);

    res.status(400).json({ error: error.message });
  }
};
// Delete a business record by ID
exports.deleteBusinessById = async (req, res) => {
  try {
    const business = await Business.findByIdAndDelete(req.params.id);
    if (!business) {
      return res.status(404).json({ error: "Business not found" });
    }
    res.status(200).json({ message: "Business deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Search businesses based on query parameters
exports.searchBusinesses = async (req, res) => {
  try {
    const query = req.query;
    const businesses = await Business.find(query);
    res.status(200).json(businesses);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update header and footer scripts for a business
exports.updateBusinessScripts = async (req, res) => {
  try {
    const { headerScripts, footerScripts } = req.body;

    const business = await Business.findByIdAndUpdate(
      req.params.id,
      {
        $set: {
          headerScripts: headerScripts || "",
          footerScripts: footerScripts || ""
        }
      },
      { new: true, runValidators: true }
    );

    if (!business) {
      return res.status(404).json({ error: "Business not found" });
    }

    res.status(200).json({
      success: true,
      message: "Scripts updated successfully",
      business: {
        _id: business._id,
        headerScripts: business.headerScripts,
        footerScripts: business.footerScripts
      }
    });
  } catch (error) {
    console.log("Error updating business scripts:", error);
    res.status(400).json({ error: error.message });
  }
};

// Get business scripts for public pages (no authentication required)
exports.getBusinessScripts = async (req, res) => {
  try {
    const business = await Business.findById(req.params.id).select('headerScripts footerScripts');

    if (!business) {
      return res.status(404).json({ error: "Business not found" });
    }

    res.status(200).json({
      success: true,
      headerScripts: business.headerScripts || "",
      footerScripts: business.footerScripts || ""
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Bulk delete businesses by IDs
exports.bulkDeleteBusinesses = async (req, res) => {
  try {
    const ids = req.body.ids;
    if (!Array.isArray(ids)) {
      return res.status(400).json({ error: "IDs should be an array" });
    }
    const result = await Business.deleteMany({ _id: { $in: ids } });
    res.status(200).json({
      message: `${result.deletedCount} businesses deleted successfully`,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Bulk edit businesses by IDs
exports.bulkEditBusinesses = async (req, res) => {
  try {
    const { ids, updates } = req.body;
    if (!Array.isArray(ids) || typeof updates !== "object") {
      return res.status(400).json({ error: "Invalid input" });
    }
    const result = await Business.updateMany(
      { _id: { $in: ids } },
      { $set: updates },
      { multi: true, runValidators: true }
    );
    res
      .status(200)
      .json({ message: `${result.nModified} businesses updated successfully` });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
