const express = require("express");
const router = express.Router();
const payementController=require("../controllers/paymentController");

router.get("/get-price-id", payementController.getPriceId);
router.get("/get-paddle-token", payementController.getPaddleToken);
router.get("/active-subscription", payementController.getActiveSubscription);
router.get("/all-subscriptions", payementController.getAllSubscriptions);

router.post("/create-portal-session", payementController.createPortalSession);
router.post("/save-subscription", payementController.saveSubscription);


module.exports=router;
