const PublicMenu = require("../models/PublicMenuModel.js");
const File = require("../models/File.js");
const path = require("path");
const fs = require("fs");
const savePublicMenu = async (req, res) => {
  try {
    const storedPublicMenu = await PublicMenu.findOne({
      businessId: req.user.businessId,
    });

    if (!storedPublicMenu) {
      return res.status(400).json({ error: "Menu not found" });
    }

    const updateQuery = {};

    // Set simple fields
    const setFields = {};
    if (req.body.fontColor) {
      setFields.fontColor = req.body.fontColor;
    }
    if (req.body.backgroundColor) {
      setFields.backgroundColor = req.body.backgroundColor;
    }
    if (req.body.fontFamily && req.body.fontWeight && req.body.fontSize) {
      setFields.font = {
        fontFamily: req.body.fontFamily,
        fontWeight: req.body.fontWeight,
        fontSize: req.body.fontSize,
      };
    }

    if (req.files?.logo) {
      try {
        const urlPath = await getUrlFromFile(req, res, "logo");
        console.log("urlPath", urlPath);
        if (urlPath) setFields.logoUrl = urlPath;
      } catch (err) {
        return res.status(500).json({ error: err.message });
      }
    } else if (req.body.logoUrl) {
      setFields.logoUrl = req.body.logoUrl;
    }

    if (req.files?.favIcon) {
      try {
        const urlPath = await getUrlFromFile(req, res, "favIcon");
        console.log("urlPath", urlPath);
        if (urlPath) setFields.favIcon = urlPath;
      } catch (err) {
        return res.status(500).json({ error: err.message });
      }
    } else if (req.body.faviconUrl) {
      setFields.favIcon = req.body.faviconUrl;
    }

    if (Object.keys(setFields).length > 0) {
      updateQuery.$set = setFields;
    }

    // Push items if available and not reorder
    if (req.body.items && req.body.items.length > 0 && !req.query?.reorder) {
      updateQuery.$push = { items: { $each: req.body.items } };
    } else if (
      req.body.items &&
      req.body.items.length > 0 &&
      req.query?.reorder
    ) {
      updateQuery.$set = { items: req.body.items };
    }

    const updatedPublicMenu = await PublicMenu.findOneAndUpdate(
      { _id: storedPublicMenu._id, businessId: req.user.businessId },
      updateQuery,
      { new: true }
    );

    if (!updatedPublicMenu) {
      return res.status(404).json({ message: "Public menu not found." });
    }

    res.status(200).json(updatedPublicMenu);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

const getPublicMenu = async (req, res) => {
  try {
    const storedPublicMenu = await PublicMenu.findOne({
      businessId: req.user.businessId,
    });

    if (!storedPublicMenu) {
      const newMenu = await PublicMenu.create({
        businessId: req.user.businessId,
        items: req.query.items,
      });
      return res.status(200).json(newMenu);
    }
    res.status(200).json(storedPublicMenu);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

const deletePublicMenu = async (req, res) => {
  try {
    const { menuId } = req.params;
    const { businessId } = req.user;

    const storedPublicMenu = await PublicMenu.findOne({
      businessId,
      "items._id": menuId,
    });

    if (!storedPublicMenu) {
      return res.status(400).json({ error: "Menu not found" });
    }

    const updatedMenu = await PublicMenu.findOneAndUpdate(
      { businessId },
      {
        $pull: { items: { _id: menuId } },
      },
      { new: true }
    );

    res.status(200).json(updatedMenu);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
const updatePublicMenu = async (req, res) => {
  try {
    const { menuId } = req.params;
    const { businessId } = req.user;
    const { text, url, target = "_blank" } = req.body;

    const storedPublicMenu = await PublicMenu.findOne({
      businessId,
      "items._id": menuId,
    });

    if (!storedPublicMenu) {
      return res.status(400).json({ error: "Menu item not found" });
    }

    const updatedMenu = await PublicMenu.findOneAndUpdate(
      { businessId, "items._id": menuId },
      {
        $set: {
          "items.$.text": text,
          "items.$.url": url,
          "items.$.target": target,
        },
      },
      { new: true }
    );

    res
      .status(200)
      .json({ message: "Menu item updated successfully", data: updatedMenu });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

async function getUrlFromFile(req, res, fileKey) {
  if (!req.files || Object.keys(req.files).length === 0) {
    throw new Error("No file attached."); // Throw instead of res.status inside utility
  }

  const file = req.files[fileKey];

  // Ensure the upload directory exists
  const uploadDir = path.join(__dirname, "../public/uploads");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Define the file path and save the file
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(uploadDir, fileName);

  // Wrap file.mv in a Promise
  return new Promise((resolve, reject) => {
    file.mv(filePath, async (err) => {
      if (err) {
        console.log(err);
        return reject(err);
      }

      try {
        const fileData = {
          fileName: fileName,
          fileType: file.mimetype,
          fileSize: file.size,
          url: `/uploads/${fileName}`,
          uploadedBy: req.user._id,
          associatedEntity: req.body.associatedEntity || [],
          entityType: req.body.entityType,
          tags: req.body.tags || [],
          customFields: req.body.customFields || {},
        };

        const savedFile = await File.create(fileData);

        return resolve(savedFile.url);
      } catch (error) {
        reject(error);
      }
    });
  });
}

module.exports = {
  savePublicMenu,
  getPublicMenu,
  deletePublicMenu,
  updatePublicMenu,
};
