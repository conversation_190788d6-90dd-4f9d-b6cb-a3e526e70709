const express = require("express");
const router = express.Router();
const businessController = require("../controllers/businessController");
const { checkPermissions } = require("../middleware/middleware");

router.get(
  "/search",
  checkPermissions("Business", ["read"]),
  businessController.searchBusinesses
);
router.post(
  "/bulk-delete",
  checkPermissions("Business", ["delete"]),
  businessController.bulkDeleteBusinesses
);
router.post(
  "/bulk-edit",
  checkPermissions("Business", ["edit"]),
  businessController.bulkEditBusinesses
);

router.post(
  "/",
  checkPermissions("Business", ["create"]),
  businessController.createBusiness
);
router.get(
  "/",
  checkPermissions("Business", ["read"]),
  businessController.getAllBusinesses
);
router.get(
  "/:id",
  checkPermissions("Business", ["read"]),
  businessController.getBusinessById
);
router.put(
  "/:id",
  checkPermissions("Business", ["update"]),
  businessController.updateBusinessById
);
router.put(
  "/notification/:id",
  checkPermissions("Notification", ["update"]),
  businessController.updateBusinessNotificationById
);
router.put(
  "/scripts/:id",
  checkPermissions("Business", ["update"]),
  businessController.updateBusinessScripts
);
router.get(
  "/scripts/:id",
  businessController.getBusinessScripts
);
router.delete(
  "/:id",
  checkPermissions("Business", ["delete"]),
  businessController.deleteBusinessById
);

module.exports = router;
