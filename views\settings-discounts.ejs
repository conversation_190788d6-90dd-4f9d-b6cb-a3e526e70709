<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Discounts" }) %> 

<%- contentFor('body') %> <%-include("partials/page-title",{"title": "Discounts" , "pagetitle": "Discounts" }) %>


 

  <div class="container">
    <div style="padding: 3rem;" class="page-content">
      <div class="container-fluid">
        <!-- Page Title -->
        <div class="row">
          <div class="col-12">
            <div
              class="page-title-box d-sm-flex align-items-center justify-content-between"
            >
              <h4 class="mb-sm-0 font-size-18">Discount Management</h4>
              <div class="page-title-right">
                <ol class="breadcrumb m-0">
                  <li class="breadcrumb-item">
                    <a href="javascript: void(0);">Dashboard</a>
                  </li>
                  <li class="breadcrumb-item active">Discounts</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Discount Card -->
        <div class="row justify-content-center">
          <div class="col-12 col-xl-10">
            <div class="card shadow-sm border-0">
              <div
                class="card-header border-0"
                style="background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);  border-radius: 0.5rem 0.5rem 0 0;"
              >
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="avatar-sm rounded-circle d-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.2); width: 45px; height: 45px;">
                      <i class="fas fa-plus-circle" style=" font-size: 20px;"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <h4 style="color: #ffffff;" class="card-title mb-1" style=" font-weight: 600; font-size: 1.25rem;">Create New Discount</h4>
                    <p class="card-title-desc mb-0" style="color: rgba(255,255,255,0.8); font-size: 14px;">Set up discount codes for your customers</p>
                  </div>
                </div>
              </div>
              <div class="card-body p-4">
                <form id="createDiscountForm">
                  <!-- Basic Information Section -->
                  <div class="mb-4">
                    <h6 class="fw-semibold text-muted mb-3">
                      <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h6>
                    <div class="row g-3">
                      <div class="col-md-8">
                        <div class="mb-3">
                          <label for="description" class="form-label fw-semibold">
                            Description <span class="text-danger">*</span>
                          </label>
                          <input
                            type="text"
                            class="form-control form-control-lg"
                            id="description"
                            name="description"
                            placeholder="Enter discount description"
                            required
                          />
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="mb-3">
                          <label for="expires_at" class="form-label fw-semibold">
                            Expires At <span class="text-danger">*</span>
                          </label>
                          <input
                            type="datetime-local"
                            class="form-control form-control-lg"
                            id="expires_at"
                            name="expires_at"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Discount Details Section -->
                  <div class="mb-4">
                    <h6 class="fw-semibold text-muted mb-3">
                      <i class="fas fa-percentage me-2"></i>Discount Details
                    </h6>
                    <div class="row g-3">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="type" class="form-label fw-semibold">
                            Discount Type <span class="text-danger">*</span>
                          </label>
                          <select
                            class="form-select form-select-lg"
                            id="type"
                            name="type"
                            required
                          >
                            <option value="">Select discount type</option>
                            <option value="percentage">Percentage (%)</option>
                            <option value="fixed">Fixed Amount ($)</option>
                          </select>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="amount" class="form-label fw-semibold">
                            Amount <span class="text-danger">*</span>
                          </label>
                          <div class="input-group input-group-lg">
                            <span class="input-group-text" id="amount-addon" style="background-color: #f8f9fa;">
                              <i class="fas fa-percentage" id="amount-icon"></i>
                            </span>
                            <input
                              type="number"
                              class="form-control"
                              id="amount"
                              name="amount"
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Options Section -->
                  <div class="mb-4">
                    <h6 class="fw-semibold text-muted mb-3">
                      <i class="fas fa-cog me-2"></i>Discount Options
                    </h6>
                    <div class="row g-3">
                      <div class="col-md-6">
                        <div class="card border h-100" style="border-color: #e9ecef !important; background-color: #f8f9fa;">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                              <input
                                class="form-check-input"
                                type="checkbox"
                                id="enabled_for_checkout"
                                name="enabled_for_checkout"
                                checked
                                style="transform: scale(1.3);"
                              />
                            </div>
                            <div class="flex-grow-1">
                              <label class="form-check-label fw-semibold mb-1 d-block" for="enabled_for_checkout">
                                Enable for Checkout
                              </label>
                              <p class="text-muted mb-0 small">Allow customers to use this discount during checkout</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card border h-100" style="border-color: #e9ecef !important; background-color: #f8f9fa;">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                              <input
                                class="form-check-input"
                                type="checkbox"
                                id="recur"
                                name="recur"
                                style="transform: scale(1.3);"
                              />
                            </div>
                            <div class="flex-grow-1">
                              <label class="form-check-label fw-semibold mb-1 d-block" for="recur">
                                Recurring Discount
                              </label>
                              <p class="text-muted mb-0 small">Apply discount to recurring subscription payments</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Recurring Options -->
                  <div class="mb-4" id="recurringOptions" style="display: none">
                    <div class="row justify-content-center">
                      <div class="col-md-6">
                        <div class="card border" style="border-color: #5156be !important; background-color: rgba(81, 86, 190, 0.05);">
                          <div class="card-body p-3">
                            <label for="maximum_recurring_intervals" class="form-label fw-semibold">
                              <i class="fas fa-repeat me-2"></i>Maximum Recurring Intervals
                            </label>
                            <input
                              type="number"
                              class="form-control form-control-lg"
                              id="maximum_recurring_intervals"
                              name="maximum_recurring_intervals"
                              min="0"
                              placeholder="Enter number"
                            />
                           
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <div class="text-center pt-3">
                    <button
                      type="submit"
                      class="btn btn-lg px-5 py-3"
                      style="
                        background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
                        border: none;
                        color: #ffffff;
                        border-radius: 50px;
                        font-weight: 600;
                        box-shadow: 0 4px 15px rgba(81, 86, 190, 0.3);
                        transition: all 0.3s ease;
                      "
                    >
                      <i class="fas fa-save me-2"></i>Create Discount
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Discounts Table -->
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="card shadow-sm border-0">
              <div
                class="card-header border-0"
                style="background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);  border-radius: 0.5rem 0.5rem 0 0;"
              >
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="avatar-sm rounded-circle d-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.2); width: 45px; height: 45px;">
                      <i class="fas fa-list" style=" font-size: 18px;"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <h4 style="color: #ffffff;"  class="card-title mb-1" style=" font-weight: 600; font-size: 1.25rem;">All Discounts</h4>
                    <p class="card-title-desc mb-0" style="color: rgba(255,255,255,0.8); font-size: 14px;">Manage your discount codes and track their usage</p>
                  </div>
                </div>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table
                    class="table table-hover mb-0"
                    id="discountsTable"
                  >
                    <thead style="background: linear-gradient(135deg, #5156be 0%, #6366f1 100%); color:#ffffff !important;">
                      <tr>
                        <th style="border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-code me-2"></i>Code
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-align-left me-2"></i>Description
                        </th>
                        <th style="border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-tag me-2"></i>Type
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-dollar-sign me-2"></i>Amount
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-toggle-on me-2"></i>Status
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-chart-line me-2"></i>Used
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600;">
                          <i class="fas fa-calendar me-2"></i>Expires
                        </th>
                        <th style=" border: none; padding: 15px; font-weight: 600; text-align: center;">
                          <i class="fas fa-cogs me-2"></i>Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody id="discountsTableBody">
                      <!-- Discounts will be loaded here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


<!-- Loading Overlay -->
<div id="loadingOverlay" class="d-none">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
<style>
  /* Custom Styles for Discount Management */


  .card {
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    border-bottom: none;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #4a4fb7 0%, #5a5fcf 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(81, 86, 190, 0.4) !important;
  }

  .form-check-input:checked {
    background-color: #5156be;
    border-color: #5156be;
  }

  .form-check-input:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
  }

  .form-control:focus, .form-select:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
  }

  .table-hover tbody tr:hover {
    background-color: rgba(81, 86, 190, 0.05);
  }

  .table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
  }

  .badge-success {
    background-color: #28a745;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
  }

  .badge-danger {
    background-color: #dc3545;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
  }

  .badge-warning {
    background-color: #ffc107;
    color: #212529;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
  }

  .bg-primary {
    background-color: #5156be !important;
  }

  .bg-info {
    background-color: #17a2b8 !important;
  }

  #loadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .action-btn {
    margin: 0 3px;
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .action-btn:hover {
    transform: translateY(-1px);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .card-body {
      padding: 1.5rem !important;
    }

    .btn-lg {
      padding: 0.75rem 2rem !important;
      font-size: 1rem !important;
    }

    .form-control-lg, .form-select-lg {
      padding: 0.75rem 1rem !important;
      font-size: 1rem !important;
    }

    .input-group-lg .input-group-text {
      padding: 0.75rem 1rem !important;
      font-size: 1rem !important;
    }

    .table-responsive {
      font-size: 14px;
    }

    .action-btn {
      padding: 4px 8px;
      font-size: 11px;
    }
  }

  @media (max-width: 576px) {
    .card-body {
      padding: 1rem !important;
    }

    .row.g-3 {
      --bs-gutter-x: 1rem;
    }

    .table thead th {
      font-size: 12px;
      padding: 10px 8px !important;
    }

    .table tbody td {
      font-size: 12px;
      padding: 10px 8px !important;
    }
  }

  /* Animation for form sections */
  .mb-4 {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Custom scrollbar for table */
  .table-responsive::-webkit-scrollbar {
    height: 8px;
  }

  .table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background: #5156be;
    border-radius: 4px;
  }

  .table-responsive::-webkit-scrollbar-thumb:hover {
    background: #4a4fb7;
  }
</style>

<%- contentFor('FooterJs') %>

<script>
  $(document).ready(function () {
    // Load discounts on page load
    loadDiscounts();

    // Handle recurring checkbox change
    $("#recur").change(function () {
      if ($(this).is(":checked")) {
        $("#recurringOptions").show(300);
        $("#maximum_recurring_intervals").attr("required", true);
      } else {
        $("#recurringOptions").hide(300);
        $("#maximum_recurring_intervals").attr("required", false);
        $("#maximum_recurring_intervals").val("");
      }
    });

    // Handle discount type change to update icon
    $("#type").change(function () {
      const selectedType = $(this).val();
      const icon = $("#amount-icon");

      if (selectedType === "percentage") {
        icon.removeClass("fa-dollar-sign").addClass("fa-percentage");
      } else if (selectedType === "fixed") {
        icon.removeClass("fa-percentage").addClass("fa-dollar-sign");
      } else {
        icon.removeClass("fa-dollar-sign").addClass("fa-percentage");
      }
    });

    // Handle form submission
    $("#createDiscountForm").submit(function (e) {
      e.preventDefault();
      createDiscount();
    });
  });

  function showLoading() {
    $("#loadingOverlay").removeClass("d-none");
  }

  function hideLoading() {
    $("#loadingOverlay").addClass("d-none");
  }

  function createDiscount() {
    showLoading();

    const formData = {
      description: $("#description").val(),
      type: $("#type").val(),
      amount: parseFloat($("#amount").val()),
      enabled_for_checkout: $("#enabled_for_checkout").is(":checked"),
      recur: $("#recur").is(":checked"),
      maximum_recurring_intervals: $("#maximum_recurring_intervals").val()
        ? parseInt($("#maximum_recurring_intervals").val())
        : null,
      expires_at: $("#expires_at").val(),
    };

    $.ajax({
      url: "/api/discounts",
      method: "POST",
      contentType: "application/json",
      data: JSON.stringify(formData),
      success: function (response) {
        hideLoading();
        showToast("Discount created successfully!", "success");
        $("#createDiscountForm")[0].reset();
        $("#recurringOptions").hide();
        loadDiscounts();
      },
      error: function (xhr) {
        hideLoading();
        const error = xhr.responseJSON?.error || "Failed to create discount";
        showToast(error, "error");
      },
    });
  }

  function loadDiscounts() {
    showLoading();

    $.ajax({
      url: "/api/discounts",
      method: "GET",
      success: function (discounts) {
        hideLoading();
        displayDiscounts(discounts);
      },
      error: function (xhr) {
        hideLoading();
        showToast("Failed to load discounts", "error");
      },
    });
  }

  function displayDiscounts(discounts) {
    const tbody = $("#discountsTableBody");
    tbody.empty();

    if (discounts.length === 0) {
      tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                            No discounts found. Create your first discount above.
                        </td>
                    </tr>
                `);
      return;
    }

    discounts.forEach((discount) => {
      const statusBadge = getStatusBadge(discount.status);
      const typeBadge = getTypeBadge(discount.type);
      const expiresAt = new Date(discount.expires_at).toLocaleDateString();
      const amount =
        discount.type === "percentage"
          ? `${discount.amount}%`
          : `$${discount.amount}`;

      tbody.append(`
                    <tr>
                        <td><strong>${discount.code}</strong></td>
                        <td>${discount.description}</td>
                        <td>${typeBadge}</td>
                        <td>${amount}</td>
                        <td>${statusBadge}</td>
                        <td><span class="badge bg-info">${discount.times_used}</span></td>
                        <td>${expiresAt}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary action-btn" onclick="viewDiscount('${discount._id}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger action-btn" onclick="deleteDiscount('${discount._id}')" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `);
    });
  }

  function getStatusBadge(status) {
    switch (status) {
      case "active":
        return '<span class="badge badge-success">Active</span>';
      case "inactive":
        return '<span class="badge badge-danger">Inactive</span>';
      default:
        return '<span class="badge badge-warning">Unknown</span>';
    }
  }

  function getTypeBadge(type) {
    switch (type) {
      case "percentage":
        return '<span class="badge bg-primary">Percentage</span>';
      case "fixed":
        return '<span class="badge bg-secondary">Fixed Amount</span>';
      default:
        return '<span class="badge bg-warning">Unknown</span>';
    }
  }

  function viewDiscount(discountId) {
    // Implement view discount functionality
    showToast("View discount functionality coming soon!", "info");
  }

  function deleteDiscount(discountId) {
   
      // Implement delete functionality
      showToast("Delete discount functionality coming soon!", "info");
    
  }

 
</script>
