<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title": "All Designs" }) %>

<!-- header content goes here -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<!-- quick action starts -->

 <style>



/* Design options container styling */
.design-options {
    position: absolute;
    top: 5px;
    right: 20px; /*Use fixed pixel value for better control*/
    display: none;
    background-color: rgba(0, 0, 0, 0.7); /* Add transparency for a modern feel */
    color: #fff;
    padding: 0px 6px; /* Add some padding for spacing */
    border-radius: 5px; /* Rounded corners for a softer look */
    z-index: 10; /* Ensure it stays on top */
    transition: background-color 0.3s ease; /* Smooth background color transition */
}

/* Ellipsis icon styling */
.design-options i {
    /* font-size: 12px; */
    color: #f9f9f9;
    cursor: pointer;
    transition: color 0.3s ease; /* Smooth transition on hover */
}

/* Hover effect for the icon */
.design-options:hover {
    background-color: #555; /* Slightly lighter background on hover */
}

.design-options i:hover {
    color: #f9f9f9; /* Gold color on hover for visibility and elegance */
}

/* Show the ellipsis icon when hovering over the design-wrapper */
.design-wrapper:hover .design-options {
    display: block;
}


.icon-box {
    text-align: center;
    padding-top: 20px;
    padding-bottom: 20px;
}

.circle-icon {
    background-color: #f0f0f0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.circle-icon i.material-icons {
    font-size: 30px;
    color: #555;
}

a {
    text-decoration: none;
    color: #000;
}

a:hover {
    text-decoration: underline;
    color: #007bff;
}

 </style>
<!-- quick action ends -->

<style>
    .search-button {
        width: 100%;
        height: 60px;
        font-size: 18px;
    }

    .single-design {
        position: relative;
        margin-bottom: 30px;
    }

    /* Edit button styles removed - now using dropdown */

    .proposal-thumbnail {
        height: 220px;
        background-color: #f8f9fa;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        border: 1px solid #ddd;
    }

    .proposal-details {
        text-align: center;
        margin-top: 10px;
        font-size: 14px;
    }

    .pagination-center {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    /* Lazy loading and design card improvements */
    .design-thumbnail {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .design-wrapper:hover .design-thumbnail {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .lazy-image {
        transition: opacity 0.3s ease;
    }

    .image-loading-placeholder {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
    }

    @keyframes loading-shimmer {
        0% {
            background-position: -200% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    /* Initial loading indicator styling */
    #initial-loading {
        min-height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    /* Pagination loading indicator */
    #loading {
        padding: 20px 0;
    }

    /* Design card hover effects */
    .single-design {
        transition: transform 0.2s ease;
        margin-bottom: 30px;
        padding: 10px;
    }

    .single-design:hover {
        transform: translateY(-3px);
    }

    /* Edit button styles removed - functionality moved to dropdown */

    /* Design actions positioning and styling */
    .design-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .design-wrapper:hover .design-actions {
        opacity: 1;
    }

    .design-actions .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .design-actions .btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Dropdown menu styling */
    .dropdown-menu {
        border: none !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        border-radius: 8px !important;
        padding: 8px 0 !important;
        margin-top: 5px !important;
    }

    .dropdown-item {
        padding: 10px 16px !important;
        transition: all 0.2s ease !important;
        font-size: 14px;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa !important;
        transform: translateX(2px);
    }

    .dropdown-item i {
        width: 16px;
        text-align: center;
    }

    /* Dropdown divider styling */
    .dropdown-divider {
        margin: 4px 0 !important;
        border-top: 1px solid #e9ecef !important;
    }

    /* Delete item styling */
    .dropdown-item.text-danger:hover {
        background-color: #f8d7da !important;
        color: #721c24 !important;
    }

    /* Ensure dropdown appears above other elements */
    .design-wrapper {
        overflow: visible !important;
    }

    .single-design {
        overflow: visible !important;
        z-index: 1;
    }

    .single-design:hover {
        z-index: 10;
    }

    /* Remove image hover effects since it's no longer clickable */

    /* Error state styling */
    .error-state {
        color: #6c757d;
        text-align: center;
        padding: 40px 20px;
    }

    .error-state i {
        margin-bottom: 15px;
    }

    /* Clickable design image styling */
    .design-thumbnail {
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .design-thumbnail:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(81, 86, 190, 0.15);
    }

    /* Hover overlay for design images */
    .design-hover-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(81, 86, 190, 0.8);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 500;
        font-size: 14px;
        z-index: 5;
    }

    .design-thumbnail:hover .design-hover-overlay {
        opacity: 1;
    }

    /* Rename success animation */
    .text-success {
        color: #28a745 !important;
        transition: color 0.3s ease;
    }

    /* Improved design card styling */
    .design-wrapper {
        background: #fff;
        border-radius: 12px;
        padding: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .design-wrapper:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-3px);
    }

    /* Proposal details styling */
    .proposal-details {
        padding: 12px 8px 8px 8px;
    }

    .proposal-details strong {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        display: block;
        margin-bottom: 4px;
        line-height: 1.3;
    }

    .proposal-details p {
        font-size: 12px;
        color: #6c757d;
        margin: 0;
    }

    .proposal-details a {
        text-decoration: none;
        color: inherit;
    }

    .proposal-details a:hover {
        text-decoration: none;
        color: #5156be;
    }
</style>


<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "All Designs" , "pagetitle": "MixCertificate" }) %>


<!-- <div class=" container mt-5 pt-5">
    Search Section
   <div class="mb-4">
        <div class="input-group">
            <input type="text" class="form-control search-button" placeholder="Search by name or industry...">
            <button class="btn btn-outline-secondary" type="button">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div> --> 

<div class="text-end">
    <a 
    class="btn btn-primary my-2"
    href="/design-deleted">Deleted Designs</a>
</div>

<div class="container mt-5 pt-5">
        <!-- Search Section -->
        <div class="mb-4">
            <input type="text" class="form-control search-button" placeholder="Search by name or industry...">
        </div>
</div>

<!-- quick create actions section starts -->
<div class="mt-5 pt-5">
    <div class="row justify-content-center text-center">
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">description</i>
                </div>
                <a href="#" onclick="openTab('Letter', 1600, 1000)">Certificate</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">bookmark</i>
                </div>
                <a href="#" onclick="openTab('Badge', 600, 600)">Badge</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">description</i>
                </div>
                <a href="#" onclick="openTab('Letter', 1600, 1120)" title="Certificate Vertical">Certificate V</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">article</i>
                </div>
                <a href="#" onclick="openTab('A4', 2481, 3508)">A4</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">description</i>
                </div>
                <a href="#" onclick="openTab('Letter', 2550, 3300)">Letter</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">description</i>
                </div>
                <a href="#" onclick="openTab('Legal', 2550, 4200)">Legal</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">newspaper</i>
                </div>
                <a href="#" onclick="openTab('Tabloid', 3300, 5100)">Tabloid</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">receipt</i>
                </div>
                <a href="#" onclick="openTab('Executive', 2175, 3150)">Executive</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">description</i>
                </div>
                <a href="#" onclick="openTab('A3', 3508, 4961)">A3</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">receipt_long</i>
                </div>
                <a href="#" onclick="openTab('A5', 1748, 2481)">A5</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">receipt_long</i>
                </div>
                <a href="#" onclick="openTab('B5', 2079, 2952)">B5</a>
            </div>
        </div>
        <div class="col-auto mb-4">
            <div class="icon-box d-flex flex-column align-items-center p-3">
                <div class="circle-icon">
                    <i class="material-icons">build</i>
                </div>
                <a href="#" onclick="openCustomSize()">Custom</a>
            </div>
        </div>
    </div>
</div>


<!-- quick create actions section ends -->


<!-- Body goes here -->
<div class="mt-5">

<!-- All design Templates Section starts -->
<div class="mt-5">
    <!-- Initial loading indicator -->
    <div id="initial-loading" class="text-center my-5">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-3">
            <h5 class="text-muted">Loading your designs...</h5>
            <p class="text-muted">Please wait while we fetch your creative works</p>
        </div>
    </div>

    <div class="row" id="all-designs" style="display: none;">
        <!-- Design templates will be inserted here dynamically -->
    </div>

    <!-- Pagination loading indicator -->
    <div id="loading" class="text-center my-4" style="display:none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading more designs...</p>
    </div>
</div>
<!-- All design Templates Section ends -->


</div>

<%- contentFor('FooterJs') %>

<!-- Custom Size Modal -->
<div class="modal fade" id="customSizeModal" tabindex="-1" aria-labelledby="customSizeLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customSizeLabel">Custom Size</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customWidth" class="form-label">Width (in pixels)</label>
                    <input type="number" class="form-control" id="customWidth" placeholder="Enter width">
                </div>
                <div class="mb-3">
                    <label for="customHeight" class="form-label">Height (in pixels)</label>
                    <input type="number" class="form-control" id="customHeight" placeholder="Enter height">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="createCustom">Create</button>
            </div>
        </div>
    </div>
</div>


<!-- quick action starts -->
<script>

    // Function to open a new tab with the certificate dimensions
    function openTab(name, width, height) {
        const url = `/design/?name=${name}&width=${width}&height=${height}`;
        window.open(url, '_blank');
    }

    // Function to handle custom size modal
    function openCustomSize() {
        const modal = new bootstrap.Modal(document.getElementById('customSizeModal'));
        modal.show();
    }

    // Function to create custom size
    $('#createCustom').on('click', function() {
        const width = $('#customWidth').val();
        const height = $('#customHeight').val();
        if (width && height) {
            const url = `/design/?name=Custom&width=${width}&height=${height}`;
            window.open(url, '_blank');
        } else {
            showToast('Please enter valid width and height.',"danger");
        }
    });

 </script>
<!-- quick action ends -->



<!-- design rendering starts -->
<script>
    $(document).ready(function () {
    let page = 1; // Keep track of pagination
    const limit = 10; // Number of designs to load per page
    let totalPages=0;
    let isFetching=false;
    let searchTerm='';

    // Function to initialize lazy loading for images
    function initializeLazyLoading() {
        // Use Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy-image');
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px', // Start loading 50px before the image comes into view
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('.lazy-image').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers that don't support Intersection Observer
            document.querySelectorAll('.lazy-image').forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy-image');
            });
        }
    }



    // Function to load and render designs
    function loadDesigns() {
        isFetching = true;

        // Show pagination loading indicator for subsequent loads
        if (page > 1) {
            $('#loading').show();
        }

        $.ajax({
            url: `/api/design?page=${page}&limit=${limit}&searchTerm=${searchTerm}`,
            method: 'GET',
            success: function (response) {
                isFetching = false;
                const {designs, pagesTotal} = response;
                totalPages = pagesTotal;

                designs.forEach(function (design) {
                const html = `
                    <div class="col-md-2 single-design" data-design-id="${design._id}">
                        <div class="design-wrapper" style="position: relative;">
                            <!-- Three dots menu positioned outside the image -->
                            <div class="design-actions" style="position: absolute; top: -5px; right: -5px; z-index: 20;">
                                <div class="btn-group dropdown">
                                    <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="border: 1px solid #dee2e6; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background: white;">
                                        <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" style="min-width: 160px;">
                                        <li><a class="dropdown-item" href="#" data-design-id="${design._id}" data-design-name="${design.designName}" data-bs-toggle="modal" data-bs-target="#renameModal">
                                            <i class="ri-pencil-line me-2 text-muted"></i>Rename
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" data-design-id="${design._id}" data-bs-toggle="modal" data-bs-target="#copyModal">
                                            <i class="ri-file-copy-line me-2 text-muted"></i>Duplicate
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" data-design-id="${design._id}" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="ri-delete-bin-line me-2 text-danger"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="design-thumbnail proposal-thumbnail" style="position: relative; height: 150px; background-color: #f8f9fa; overflow: hidden; border-radius: 8px; cursor: pointer;" onclick="window.open('/design/${design._id}', '_blank')">
                                <!-- Loading placeholder -->
                                <div class="image-loading-placeholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                    <div class="spinner-border spinner-border-sm text-muted" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>

                                <!-- Lazy loaded image -->
                                <img class="lazy-image"
                                     data-src="${design.imageURL}"
                                     alt="${design.designName}"
                                     loading="lazy"
                                     style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0; opacity: 0; transition: all 0.3s ease;"
                                     onload="this.style.opacity=1; this.parentElement.querySelector('.image-loading-placeholder').style.display='none';"
                                     onerror="this.parentElement.querySelector('.image-loading-placeholder').innerHTML='<i class=\\'fas fa-image text-muted\\' style=\\'font-size: 2rem;\\'></i><br><small class=\\'text-muted\\'>Image not available</small>';">

                                <!-- Hover overlay -->
                                <div class="design-hover-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(81, 86, 190, 0.8); opacity: 0; transition: opacity 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-weight: 500;">
                                    <i class="fas fa-external-link-alt me-2"></i>Open Design
                                </div>
                            </div>

                            <div class="proposal-details">
                                <a href="/design/${design._id}">
                                    <strong>${design.designName}</strong>
                                </a>
                                <p>Author: ${design.creatorName || 'Unknown'}</p>
                            </div>
                        </div>
                    </div>`;

                $('#all-designs').append(html);
            });

            // Initialize lazy loading for newly added images
            initializeLazyLoading();

            // Hide initial loading indicator and show designs container
            if (page === 1) {
                $('#initial-loading').hide();
                $('#all-designs').show();
            }

            // Hide pagination loading indicator
            $('#loading').hide();
        },
        error: function (err) {
            console.log('Error loading designs:', err);
            isFetching = false;
            $('#loading').hide();

            // Show error message if it's the first load
            if (page === 1) {
                $('#initial-loading').html(`
                    <div class="text-center my-5">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">Failed to load designs</h5>
                        <p class="text-muted">Please try refreshing the page</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh me-2"></i>Refresh Page
                        </button>
                    </div>
                `);
            }
        }
    });
}

    // Function to remove design from DOM
    function removeDesignFromDOM(designId) {
        const designElement = $(`.single-design[data-design-id="${designId}"]`);
        if (designElement.length) {
            designElement.fadeOut(300, function() {
                $(this).remove();
            });
        }
    }

    // Function to update design name in DOM
    function updateDesignNameInDOM(designId, newName) {
        const designElement = $(`.single-design[data-design-id="${designId}"]`);
        if (designElement.length) {
            // Update the design name in the title
            designElement.find('.proposal-details strong').text(newName);

            // Update all data-design-name attributes in dropdown items
            designElement.find('[data-design-name]').attr('data-design-name', newName);

            // Update the image alt attribute
            designElement.find('.lazy-image').attr('alt', newName);

            // Add a subtle animation to indicate the change
            designElement.find('.proposal-details strong').addClass('text-success');
            setTimeout(function() {
                designElement.find('.proposal-details strong').removeClass('text-success');
            }, 2000);
        }
    }

    // Add these functions to window object for global access
    window.loadDesigns = loadDesigns;
    window.removeDesignFromDOM = removeDesignFromDOM;
    window.updateDesignNameInDOM = updateDesignNameInDOM;



    // Initial load of designs
    loadDesigns();

    $(document).on("change input",".search-button",function () {
        console.log('searching...');
        debounceSearch();
    });
    let intervalId;
    function debounceSearch(){
        clearTimeout(intervalId);
        intervalId = setTimeout(function () {
            searchTerm = DOMPurify.sanitize($('.search-button').val());
            page = 1;
            $('#all-designs').empty();
            loadDesigns();
        }, 1000);
    }

    // Infinite scroll to load more designs when nearing bottom of the page
    $(window).scroll(function () {

        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
            if(page < totalPages && !isFetching){
                page++;
                loadDesigns();

            }
        }
    });

    // Show context menu options (duplicate, rename, delete) on hover
    $(document).on('mouseenter', '.design-wrapper', function () {
        $(this).find('.design-options').fadeIn();
        $(this).find('.design-hover-overlay').css('opacity', '1');
    });

    $(document).on('mouseleave', '.design-wrapper', function () {
        $(this).find('.design-options').fadeOut();
        $(this).find('.design-hover-overlay').css('opacity', '0');
    });

 

    function updateAccess(id) {
        const access = prompt('Enter access level (public/private):');
        if (access) {
            $.ajax({
                url: `https://mixcertificate-dev.mixcommerce.co/api/design/${id}`,
                method: 'PUT',
                data: { access },
                success: function (response) {
                    showToast('Access updated successfully.',"primary");
                    // Optionally, refresh the design list
                    location.reload();
                },
                error: function (err) {
                    console.log('Error updating access:', err);
                }
            });
        }
    }
});

 </script>
<!-- design rendering ends -->






<!-- action handler starts -->
 <script>
    $(document).ready(function () {
    // Handle Rename Action
    $('#renameModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget); // Button that triggered the modal
        const designId = button.data('design-id'); // Extract info from data-* attributes
        const designName = button.data('design-name'); // Get the existing design name
        const modal = $(this);

        modal.find('#renameInput').val(designName); // Set the current design name in the input
        modal.find('.confirm-rename-btn').data('design-id', designId); // Set the design ID in the button
    });

    $('#renameModal .confirm-rename-btn').on('click', function () {

        const designId = $(this).data('design-id');
        const newName = DOMPurify.sanitize($('#renameInput').val().trim());

        if (newName) {
            // Show loading state
            const button = $(this);
            const originalText = button.text();
            button.text('Renaming...').prop('disabled', true);

            // Perform AJAX request to rename the design
            $.ajax({
                url: `/api/design/${designId}`,
                method: 'PUT',
                data: { designName: newName },
                success: function (response) {
                    console.log('Design renamed successfully:', response.message);

                    // Update the design name in DOM instead of reloading
                    window.updateDesignNameInDOM(designId, newName);

                    // Close modal and show success message
                    $('#renameModal').modal('hide');
                    showToast('Design renamed successfully!', "success");
                },
                error: function (err) {
                    console.log('Error renaming design:', err);
                    showToast('Error renaming design. Please try again.', "danger");
                },
                complete: function() {
                    // Reset button state
                    button.text(originalText).prop('disabled', false);
                }
            });
        } else {
            showToast('Please enter a valid name.',"danger");
        }
    });

    // Handle Duplicate Action
    $('#copyModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget); // Button that triggered the modal
        const designId = button.data('design-id'); // Get the design ID
        const modal = $(this);
        modal.find('.confirm-copy-btn').data('design-id', designId); // Set the design ID in the button
    });

    $('#copyModal .confirm-copy-btn').on('click', function () {
        const designId = $(this).data('design-id');

        // Show loading state
        const button = $(this);
        const originalText = button.text();
        button.text('Duplicating...').prop('disabled', true);

        // Perform AJAX request to duplicate the design
        $.ajax({
            url: `/api/design/${designId}/duplicate`,
            method: 'POST',
            success: function (response) {
                console.log('Design duplicated successfully:', response.designName);

                // Close modal and show success message
                $('#copyModal').modal('hide');
                showToast('Design duplicated successfully!', "success");

                // Reload designs to show the new duplicate
                // Reset pagination and reload first page to show the new design
                page = 1;
                $('#all-designs').empty();
                window.loadDesigns();
            },
            error: function (err) {
                console.log('Error duplicating design:', err);
                showToast('Error duplicating design. Please try again.', "danger");
            },
            complete: function() {
                // Reset button state
                button.text(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Delete Action
    $('#deleteModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const designId = button.data('design-id');
        const modal = $(this);
        modal.find('.confirm-delete-btn').data('design-id', designId); // Set the design ID in the button
    });

    $('#deleteModal .confirm-delete-btn').on('click', function () {
        const designId = $(this).data('design-id');

        // Show loading state
        const button = $(this);
        const originalText = button.text();
        button.text('Deleting...').prop('disabled', true);

        // Perform AJAX request to delete the design
        $.ajax({
            url: `/api/design/${designId}/update-isdeleted`,
            method: 'PUT',
            success: function (response) {
                console.log('Design deleted successfully:', response.message);

                // Remove the design from DOM instead of reloading
                window.removeDesignFromDOM(designId);

                // Close modal and show success message
                $('#deleteModal').modal('hide');
                showToast('Design moved to trash successfully!', "success");
            },
            error: function (err) {
                console.log('Error deleting design:', err);
                showToast('Error deleting design. Please try again.', "danger");
            },
            complete: function() {
                // Reset button state
                button.text(originalText).prop('disabled', false);
            }
        });
    });


    // Handle Copy Link Action
    $('.shareFileBtn').on('click', function (event) {


        event.preventDefault();
        const fileUrl = $(this).data('file-url');

        showToast(fileUrl,"primary")

        // Copy the link to the clipboard
        navigator.clipboard.writeText(fileUrl).then(() => {
            showToast('Link copied to clipboard!',"primary");
        }).catch(err => {
            console.error('Error copying link:', err);
        });
    });
});

 </script>
<!-- action handler ends -->

 <!-- Copy Modal -->
<div class="modal fade" id="copyModal" tabindex="-1" aria-labelledby="copyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="copyModalLabel">Make a Copy of Design</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to make a copy of this design?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary confirm-copy-btn">Make a Copy</button>
            </div>
        </div>
    </div>
</div>

<!-- Rename Modal -->
<div class="modal fade" id="renameModal" tabindex="-1" aria-labelledby="renameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renameModalLabel">Rename Design</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="text" id="renameInput" class="form-control" placeholder="Enter new design name">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary confirm-rename-btn">Rename</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to move the  design to trash ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger confirm-delete-btn">Delete</button>
            </div>
        </div>
    </div>
</div>
