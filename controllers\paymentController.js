const Payment = require("../models/Payment");
const sendMail = require("../utils/email");

exports.getPriceId = async (req, res) => {
  try {
    const { billingCycle, planTier } = req.query;
    console.log(billingCycle, planTier, "billingCycle, planTier");
    let priceId = "";
    if (billingCycle === "monthly") {
      switch (planTier) {
        case "Free":
          priceId = process.env.PADDLE_FREE_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Standard":
          priceId = process.env.PADDLE_STANDARD_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Professional":
          priceId = process.env.PADDLE_PROFESSIONAL_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Enterprise":
          priceId = process.env.PADDLE_ENTERPRISE_PLAN_MONTHLY_PRICE_ID;
          break;
        default:
          priceId = "";
      }
    } else {
      switch (planTier) {
        case "Free":
          priceId = process.env.PADDLE_FREE_PLAN_YEARLY_PRICE_ID;
          break;
        case "Standard":
          priceId = process.env.PADDLE_STANDARD_PLAN_YEARLY_PRICE_ID;
          break;
        case "Professional":
          priceId = process.env.PADDLE_PROFESSIONAL_PLAN_YEARLY_PRICE_ID;
          break;
        case "Enterprise":
          priceId = process.env.PADDLE_ENTERPRISE_PLAN_YEARLY_PRICE_ID;
          break;
        default:
          priceId = "";
      }
    }
    res.status(200).json({ priceId, success: true });
  } catch (err) {
    res.status(400).json({ error: err.message, success: false });
  }
};

exports.getPaddleToken = async (req, res) => {
  try {
    res
      .status(200)
      .json({ token: process.env.PADDLE_CLIENT_TOKEN || "", success: true });
  } catch (err) {
    res.status(400).json({ error: err.message, success: false });
  }
};

exports.getAllSubscriptions = async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    // Get all subscriptions for the user, sorted by creation date (newest first)
    const subscriptions = await Payment.find({
      userId: userId,
    }).sort({ createdAt: -1 });

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(200).json({
        message: "No subscriptions found",
        success: true,
        subscriptions: [],
      });
    }

    // Convert the subscription documents to plain objects
    const subscriptionObjs = subscriptions.map((sub) => sub.toObject());

    return res.status(200).json({
      success: true,
      subscriptions: subscriptionObjs,
    });
  } catch (err) {
    console.error("Error fetching subscriptions:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.getActiveSubscription = async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    const subscription = await Payment.findOne({
      userId,
    });

    if (!subscription) {
      return res.status(200).json({
        message: "No active subscription found",
        success: false,
        subscription: null,
      });
    }

    // Convert the subscription document to a plain object
    const subscriptionObj = subscription.toObject();

    res.status(200).json({
      success: true,
      subscription: {
        id: subscriptionObj._id,
        paddleSubscriptionId: subscriptionObj.paddleSubscriptionId,
        planName: subscriptionObj.planName,
        billingCycle: subscriptionObj.billingCycle,
        startDate: subscriptionObj.startDate,
        nextBillingDate: subscriptionObj.nextBillingDate,
        paymentStatus: subscriptionObj.paymentStatus,
        isSubscriptionCancelled: subscriptionObj.isSubscriptionCancelled,
        isAutoRenew: subscriptionObj.isAutoRenew,
        endDate: subscriptionObj.endDate,
        createdAt: subscriptionObj.createdAt,
        updatedAt: subscriptionObj.updatedAt,
        certificateLimit: subscriptionObj.certificateLimit,
        paddleTransactionId: subscriptionObj.paddleTransactionId,
        totalAmountPaid: subscriptionObj.totalAmountPaid,
        paymentMethod: subscriptionObj.paymentMethod,
        cancelledAt: subscriptionObj.cancelledAt,
        customer_id: subscriptionObj.customer_id,
      },
    });
  } catch (err) {
    console.error("Error fetching active subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.manageSubscriptionPortal = async (req, res) => {
  try {
    const { subscriptionId } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!subscriptionId) {
      return res.status(400).json({
        error: "Subscription ID is required",
        success: false,
      });
    }

    const apiKey = process.env.PADDLE_API_KEY;
    const paddleApiUrl = "https://sandbox-api.paddle.com";

    console.log(
      "Generating customer portal URL for subscription:",
      subscriptionId
    );

    const response = await fetch(
      `${paddleApiUrl}/customers/${userId}/portal-sessions`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          subscription_ids: [subscriptionId.toString()],
        }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json({
        error: data.error || "Failed to create customer portal session",
        success: false,
      });
    }

    return res.status(200).json({
      url: data?.data?.url || null,
      success: true,
    });
  } catch (error) {
    console.error("Error generating subscription portal URL:", error);
    return res.status(500).json({
      error: "Internal server error",
      success: false,
    });
  }
};

exports.cancelSubscription = async (req, res) => {
  try {
    const { subscriptionId, effectiveFrom = "immediately" } = req.body;
    let userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!subscriptionId) {
      return res.status(400).json({
        error: "Subscription ID is required",
        success: false,
      });
    }

    // Validate effectiveFrom value
    if (
      effectiveFrom !== "immediately" &&
      effectiveFrom !== "next_billing_period"
    ) {
      return res.status(400).json({
        error:
          "effectiveFrom must be either 'immediately' or 'next_billing_period'",
        success: false,
      });
    }

    // Use the Paddle API key for authentication
    const apiKey = process.env.PADDLE_API_KEY;
    const paddleApiUrl = "https://sandbox-api.paddle.com";

    const response = await fetch(
      `${paddleApiUrl}/subscriptions/${subscriptionId}/cancel`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({ effective_from: effectiveFrom }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json({
        error: data.error?.message || "Failed to cancel subscription",
        success: false,
        details: data,
      });
    }

    let paymentData = "";
    if (effectiveFrom === "immediately") {
      await Payment.findOneAndDelete({
        paddleSubscriptionId: subscriptionId,
        userId: req.user?._id,
      });
    } else {
      paymentData = await Payment.findOneAndUpdate(
        { paddleSubscriptionId: subscriptionId, userId: req.user?._id },
        {
          isSubscriptionCancelled: true,
          cancelledAt: new Date(),
          isAutoRenew: false,
          updatedAt: new Date(),
        },
        {
          new: true,
        }
      );
    }

    const cancellationDate =
      effectiveFrom === "immediately"
        ? "immediately"
        : `at the end of your current billing period on ${new Date(
            paymentData.nextBillingDate
          ).toLocaleDateString()}`;

    const messageAsPerUserDecision =
      effectiveFrom === "immediately"
        ? `Dear ${
            req.user.firstName || "User"
          },\n\nYour subscription has been successfully cancelled effective ${cancellationDate}. If you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.\n\nBest regards,\nThe MixCertificate Team`
        : `Dear ${
            req.user.firstName || "User"
          },\n\nYour subscription has been scheduled for cancellation ${cancellationDate}. You will continue to have access to your subscription until this date.\n\nIf you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.\n\nBest regards,\nThe MixCertificate Team`;

    await sendMail({
      email: req.user.email,
      subject: "Subscription Cancellation Confirmation",
      message: messageAsPerUserDecision,
    });

    res.status(200).json({
      message: "Subscription cancelled successfully",
      success: true,
      data: data.data,
    });
  } catch (err) {
    console.error("Error cancelling subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.saveSubscription = async (req, res) => {
  try {
    const {
      planName,
      certificateLimit,
      paddleTransactionId,
      paymentStatus,
      paymentMethod,
      totalAmountPaid,
      billingCycle,
      customer_id,
    } = req.body;

    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!paddleTransactionId) {
      return res.status(400).json({
        error: "Transaction ID is required",
        success: false,
      });
    }

    const apiKey = process.env.PADDLE_API_KEY;
    const paddleApiUrl = "https://sandbox-api.paddle.com";

    let paddleSubscriptionId = null;

    try {
      const response = await fetch(
        `${paddleApiUrl}/transactions/${paddleTransactionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
          },
        }
      );

      const parsedResponse = await response.json();

      if (response.ok && parsedResponse.data?.subscription_id) {
        paddleSubscriptionId = parsedResponse.data.subscription_id;
      }
    } catch (error) {
      console.error("Exception while fetching transaction details:", error);
    }

    if (!paddleSubscriptionId) {
      return res.status(400).json({
        error: "Failed to fetch subscription ID from Paddle",
        success: false,
      });
    }

    const startDate = new Date();
    const nextBillingDate = new Date(startDate);
    if (billingCycle === "monthly") {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    } else if (billingCycle === "yearly") {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
    }

    const newSubscription = new Payment({
      userId,
      planName,
      certificateLimit,
      paddleSubscriptionId,
      paddleTransactionId,
      paymentStatus,
      paymentMethod,
      totalAmountPaid,
      billingCycle,
      startDate,
      nextBillingDate,
      isSubscriptionCancelled: false,
      isAutoRenew: true,
      customer_id,
    });

    await newSubscription.save();
    const fullName =
      [req.user.firstName, req.user.lastName].filter(Boolean).join(" ") ||
      "User";

    await sendMail({
      email: req.user.email,
      subject: "Subscription Purchased Successfully",
      message: `
  <p>Dear ${fullName},</p>
  <p>Thank you for your purchase! We are pleased to confirm your subscription.</p>
  <ul>
    <li><strong>Plan Name:</strong> ${planName}</li>
    <li><strong>Certificate Limit:</strong> ${certificateLimit}</li>
    <li><strong>Transaction ID:</strong> ${paddleTransactionId}</li>
    <li><strong>Payment Status:</strong> ${paymentStatus}</li>
    <li><strong>Payment Method:</strong> ${paymentMethod}</li>
    <li><strong>Total Amount Paid:</strong> $${totalAmountPaid}</li>
    <li><strong>Billing Cycle:</strong> ${billingCycle}</li>
    <li><strong>Customer ID:</strong> ${customer_id}</li>
  </ul>
  <p>If you have any questions, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
  <p>Best regards,<br/>The MixCertificate Team</p>
`,
    });

    res.status(200).json({
      message: "Subscription created successfully",
      success: true,
      subscription: {
        id: newSubscription._id,
        paddleSubscriptionId: newSubscription.paddleSubscriptionId,
        planName: newSubscription.planName,
        billingCycle: newSubscription.billingCycle,
        startDate: newSubscription.startDate,
        nextBillingDate: newSubscription.nextBillingDate,
      },
    });
  } catch (err) {
    console.error("Error saving subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.createPortalSession = async (req, res) => {
  try {
    const userId = req.user._id;
    const { customer_id } = req.body;
    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    // Use the Paddle API key for authentication
    const apiKey = process.env.PADDLE_API_KEY;
    console.log("customer_id", customer_id);
    console.log("apiKey", apiKey);
    const paddleApiUrl = "https://sandbox-api.paddle.com";

    const response = await fetch(
      `${paddleApiUrl}/customers/${customer_id}/portal-sessions`,
      {
        method: "POST",
        headers: {
          
          Authorization: `Bearer ${apiKey}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json({
        error: data.error || "Failed to create customer portal session",
        success: false,
      });
    }
    console.log("data", data);

    return res.status(200).json({
      url: data?.data?.urls.general.overview || null,
      success: true,
    });
  } catch (error) {
    console.error("Error generating customer portal session:", error);
    return res.status(500).json({
      error: "Internal server error",
      success: false,
    });
  }
};
