const Discount = require("../models/Discount");
const Payment = require("../models/Payment");
const Plan = require("../models/Plan");
const sendMail = require("../utils/email");

exports.getPriceId = async (req, res) => {
  try {
    const { billingCycle, planTier } = req.query;
    console.log(billingCycle, planTier, "billingCycle, planTier");
    let priceId = "";
    if (billingCycle === "monthly") {
      switch (planTier) {
        case "Free":
          priceId = process.env.PADDLE_FREE_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Standard":
          priceId = process.env.PADDLE_STANDARD_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Professional":
          priceId = process.env.PADDLE_PROFESSIONAL_PLAN_MONTHLY_PRICE_ID;
          break;
        case "Enterprise":
          priceId = process.env.PADDLE_ENTERPRISE_PLAN_MONTHLY_PRICE_ID;
          break;
        default:
          priceId = "";
      }
    } else {
      switch (planTier) {
        case "Free":
          priceId = process.env.PADDLE_FREE_PLAN_YEARLY_PRICE_ID;
          break;
        case "Standard":
          priceId = process.env.PADDLE_STANDARD_PLAN_YEARLY_PRICE_ID;
          break;
        case "Professional":
          priceId = process.env.PADDLE_PROFESSIONAL_PLAN_YEARLY_PRICE_ID;
          break;
        case "Enterprise":
          priceId = process.env.PADDLE_ENTERPRISE_PLAN_YEARLY_PRICE_ID;
          break;
        default:
          priceId = "";
      }
    }
    res.status(200).json({ priceId, success: true });
  } catch (err) {
    res.status(400).json({ error: err.message, success: false });
  }
};

exports.getPaddleToken = async (req, res) => {
  try {
    res
      .status(200)
      .json({ token: process.env.PADDLE_CLIENT_TOKEN || "", success: true });
  } catch (err) {
    res.status(400).json({ error: err.message, success: false });
  }
};

exports.getAllSubscriptions = async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    // Get all subscriptions for the user, sorted by creation date (newest first)
    const subscriptions = await Payment.find({
      userId: userId,
    }).sort({ createdAt: -1 });

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(200).json({
        message: "No subscriptions found",
        success: true,
        subscriptions: [],
      });
    }

    // Convert the subscription documents to plain objects
    const subscriptionObjs = subscriptions.map((sub) => sub.toObject());

    return res.status(200).json({
      success: true,
      subscriptions: subscriptionObjs,
    });
  } catch (err) {
    console.error("Error fetching subscriptions:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.getActiveSubscription = async (req, res) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    const subscription = await Payment.findOne({
      userId,
      isSubscriptionCancelled: false,
    }).populate('planId userId');

    if (!subscription) {
      return res.status(200).json({
        message: "No active subscription found",
        success: false,
        subscription: null,
      });
    }

    // Convert the subscription document to a plain object
    const subscriptionObj = subscription.toObject();

    res.status(200).json({
      success: true,
      subscription: {
        id: subscriptionObj._id,
        paddleSubscriptionId: subscriptionObj.paddleSubscriptionId,
        planName: subscriptionObj.planName,
        billingCycle: subscriptionObj.billingCycle,
        startDate: subscriptionObj.startDate,
        nextBillingDate: subscriptionObj.nextBillingDate,
        paymentStatus: subscriptionObj.paymentStatus,
        isSubscriptionCancelled: subscriptionObj.isSubscriptionCancelled,
        isAutoRenew: subscriptionObj.isAutoRenew,
        endDate: subscriptionObj.endDate,
        createdAt: subscriptionObj.createdAt,
        updatedAt: subscriptionObj.updatedAt,
        certificateLimit: subscriptionObj.certificateLimit,
        paddleTransactionId: subscriptionObj.paddleTransactionId,
        totalAmountPaid: subscriptionObj.totalAmountPaid,
        paymentMethod: subscriptionObj.paymentMethod,
        cancelledAt: subscriptionObj.cancelledAt,
        customer_id: subscriptionObj.customer_id,
        plan: subscriptionObj.planId,
        user: subscriptionObj.userId,

      },
    });
  } catch (err) {
    console.error("Error fetching active subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.saveSubscription = async (req, res) => {
  try {
    const {
      planName,
      certificateLimit,
      contactLimit,
      badgeLimit,
      eventLimit,
      paddleTransactionId,
      paymentStatus,
      paymentMethod,
      totalAmountPaid,
      billingCycle,
      customer_id,
      discountId,
      planId,
    } = req.body;

    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!paddleTransactionId) {
      return res.status(400).json({
        error: "Transaction ID is required",
        success: false,
      });
    }

    if (discountId) {
      const existingDiscount = await Discount.findOne({
        discountId,
        userId: req.user?.id,
        businessId: req.user?.businessId,
        status: "active",
        expires_at: { $gte: new Date() },
      });

      if (existingDiscount) {
        if (
          existingDiscount.maximum_recurring_intervals === null ||
          existingDiscount.times_used <
            existingDiscount.maximum_recurring_intervals
        ) {
          existingDiscount.times_used += 1;
          if (
            existingDiscount.maximum_recurring_intervals !== null &&
            existingDiscount.times_used ===
              existingDiscount.maximum_recurring_intervals
          ) {
            existingDiscount.status = "inactive";
          }

          await existingDiscount.save();
        }
      }
    }

    const apiKey = process.env.PADDLE_API_KEY;
    const paddleApiUrl = "https://sandbox-api.paddle.com";

    let paddleSubscriptionId = null;

    try {
      const response = await fetch(
        `${paddleApiUrl}/transactions/${paddleTransactionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
          },
        }
      );

      const parsedResponse = await response.json();

      if (response.ok && parsedResponse.data?.subscription_id) {
        paddleSubscriptionId = parsedResponse.data.subscription_id;
      }
    } catch (error) {
      console.error("Exception while fetching transaction details:", error);
    }

    if (!paddleSubscriptionId) {
      return res.status(400).json({
        error: "Failed to fetch subscription ID from Paddle",
        success: false,
      });
    }

    const startDate = new Date();
    const nextBillingDate = new Date(startDate);
    if (billingCycle === "monthly") {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    } else if (billingCycle === "yearly") {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
    }

    await Plan.findByIdAndUpdate(planId, { status: "active" });

    const newSubscription = new Payment({
      userId,
      planId,
      planName,
      certificateLimit,
      contactLimit,
      badgeLimit,
      eventLimit,
      paddleSubscriptionId,
      paddleTransactionId,
      paymentStatus,
      paymentMethod,
      totalAmountPaid,
      billingCycle,
      startDate,
      nextBillingDate,
      isSubscriptionCancelled: false,
      isAutoRenew: true,
      customer_id,
      
    });

    await newSubscription.save();
    const fullName =
      [req.user.firstName, req.user.lastName].filter(Boolean).join(" ") ||
      "User";

    await sendMail({
      email: req.user.email,
      subject: "Subscription Purchased Successfully",
      message: `
  <p>Dear ${fullName},</p>
  <p>Thank you for your purchase! We are pleased to confirm your subscription.</p>
  <ul>
    <li><strong>Plan Name:</strong> ${planName}</li>
    <li><strong>Certificate Limit:</strong> ${certificateLimit}</li>
    <li><strong>Contact Limit:</strong> ${contactLimit}</li>
    <li><strong>Badge Limit:</strong> ${badgeLimit}</li>
    <li><strong>Event Limit:</strong> ${eventLimit}</li>
    <li><strong>Transaction ID:</strong> ${paddleTransactionId}</li>
    <li><strong>Payment Status:</strong> ${paymentStatus}</li>
    <li><strong>Payment Method:</strong> ${paymentMethod}</li>
    <li><strong>Total Amount Paid:</strong> $${totalAmountPaid}</li>
    <li><strong>Billing Cycle:</strong> ${billingCycle}</li>
    <li><strong>Customer ID:</strong> ${customer_id}</li>
  </ul>
  <p>If you have any questions, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
  <p>Best regards,<br/>The MixCertificate Team</p>
`,
    });

    res.status(200).json({
      message: "Subscription created successfully",
      success: true,
      subscription: {
        id: newSubscription._id,
        paddleSubscriptionId: newSubscription.paddleSubscriptionId,
        planName: newSubscription.planName,
        billingCycle: newSubscription.billingCycle,
        startDate: newSubscription.startDate,
        nextBillingDate: newSubscription.nextBillingDate,
      },
    });
  } catch (err) {
    console.error("Error saving subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
};

exports.createPortalSession = async (req, res) => {
  try {
    const userId = req.user._id;
    const { customer_id, subscription_id } = req.body;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!customer_id) {
      return res.status(400).json({
        error: "Customer ID is required",
        success: false,
      });
    }

    // Use the Paddle API key for authentication
    const apiKey = process.env.PADDLE_API_KEY;

    if (!apiKey) {
      return res.status(500).json({
        error: "Paddle API key not configured",
        success: false,
      });
    }

    const paddleApiUrl = "https://sandbox-api.paddle.com";

    // Prepare the request body according to Paddle API v1 documentation
    const requestBody = {};

    // Only include subscription_ids if subscription_id is provided
    if (subscription_id) {
      requestBody.subscription_ids = [subscription_id.toString()];
    }

    const response = await fetch(
      `${paddleApiUrl}/customers/${customer_id}/portal-sessions`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      console.error("Paddle API error:", data);

      // Handle specific error cases
      if (response.status === 403) {
        return res.status(403).json({
          error:
            "Not authorized to create customer portal session. Please check your Paddle API key permissions.",
          success: false,
          details: data,
        });
      }

      return res.status(response.status).json({
        error:
          data.error?.message ||
          data.error ||
          "Failed to create customer portal session",
        success: false,
        details: data,
      });
    }

    // Extract the portal URL from the response
    const portalUrl = data?.data?.urls?.general?.overview || null;

    if (!portalUrl) {
      console.error("No portal URL found in response:", data);
      return res.status(500).json({
        error: "Portal URL not found in response",
        success: false,
      });
    }

    return res.status(200).json({
      url: portalUrl,
      success: true,
    });
  } catch (error) {
    console.error("Error generating customer portal session:", error);
    return res.status(500).json({
      error: "Internal server error",
      success: false,
    });
  }
};
