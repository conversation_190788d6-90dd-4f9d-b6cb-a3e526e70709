const csv = require("csv-parser");
const fs = require("fs");
const path = require("path");
const { Parser } = require("json2csv");
const moment = require("moment");
const { v4: uuidv4 } = require("uuid");
const sanitizer = require("sanitizer");
const ImportHistory = require("../models/ImportHistory"); // Import the ImportHistory model
const Contact = require("../models/Contact");
const   mongoose = require("mongoose");

// Create a new contact
exports.createContact = async (req, res) => {
  try {

    req.body.businessEmail = sanitizer.sanitize(req.body.businessEmail || '');
    req.body.fullName = sanitizer.sanitize(req.body.fullName || '');

    const isContactAlreadyExists = await Contact.findOne({
      businessEmail: req.body.businessEmail.trim(),
    });

    if (isContactAlreadyExists) {
      return res.status(400).json({ error: "Contact already exists" });
    }
    const contact = new Contact(req.body);

    contact.creatorId = req.user._id; // Set the userId to the current user's ID

    //console.log("new contact: ",req.body)

    // saving contact in db
    await contact.save();

    res.status(201).json(contact);
  } catch (error) {
    console.log(error);
    res.status(400).json({ error: error.message });
  }
};

exports.importContactsNew = async (req, res) => {
  const { csvFileUrl } = req.body;

  if (!csvFileUrl) {
    return res.status(400).json({ error: "No CSV file URL provided" });
  }

  // Construct the local file path from the URL
  const baseUrl = `${req.protocol}://${req.get("host")}`;
  const relativeFilePath = csvFileUrl.replace(`${baseUrl}/`, "");
  const filePath = path.join(__dirname, "../public/", relativeFilePath);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "File not found" });
  }

  let contacts = [];
  let ignoredContacts = [];
  let startTime = Date.now();

  const readStream = fs.createReadStream(filePath).pipe(csv());

  for await (const row of readStream) {
    row.creatorId = req.user._id;
    row.src = "csv import";

    const existingContact = await Contact.findOne({
      businessEmail: row.businessEmail,
    });

    if (existingContact) {
      ignoredContacts.push({
        ...row,
        reason: "Email already exists in the database",
      });
    } else {
      contacts.push(row);
    }
  }

  try {
    let totalImported = 0;
    let importStatus = "Failed";
    if (contacts.length > 0) {
      const result = await Contact.insertMany(contacts);
      totalImported = result.length;
      importStatus = "Completed";
    }

    let totalIgnored = ignoredContacts.length;
    let durationToImport = (Date.now() - startTime) / 1000; // Duration in seconds
    let ignoredFileURL = "";

    // If there are ignored contacts, create the CSV file using json2csv
    if (totalIgnored > 0) {
      const uploadsDir = path.join(__dirname, "../public/uploads");
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true }); // Create the directory if it doesn't exist
      }

      const fileName = `ignored_contacts_${path.basename(
        filePath,
        ".csv"
      )}_${Date.now()}.csv`;
      const ignoredFilePath = path.join(uploadsDir, fileName);

      // Define CSV fields
      const fields = ["creatorId", "src", "businessEmail", "reason"];
      const csvData = parse(ignoredContacts, { fields }); // Convert JSON to CSV

      // Write CSV data to file
      fs.writeFileSync(ignoredFilePath, csvData);

      // Save the URL of the ignored contacts CSV file
      ignoredFileURL = `${baseUrl}/uploads/${fileName}`;
    }

    // Save the import history
    const importHistory = new ImportHistory({
      importedFileName: path.basename(filePath),
      importedFileURL: csvFileUrl,
      importedBy: req.user._id,
      importType: "Contacts Import",
      totalImported: totalImported,
      totalIgnored: totalIgnored,
      ignoredFileURL: ignoredFileURL,
      durationToImport: durationToImport,
      status: importStatus,
    });

    await importHistory.save();

    fs.unlinkSync(filePath); // Clean up the uploaded file

    return res.status(200).json({
      message: `${totalImported} Contacts imported, ${totalIgnored} Contacts ignored`,
      totalImported,
      totalIgnored,
      ignoredContacts,
      ignoredFileURL,
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

exports.importContacts = async (req, res) => {
  const { csvFileUrl, mapping, detectedVariables, manualVariables } = req.body;

  const userId = req.user._id;

  if (!csvFileUrl || !mapping) {
    return res
      .status(400)
      .json({ error: "CSV file URL and field mapping are required." });
  }

  const baseUrl = `${req.protocol}://${req.get("host")}`;
  const relativePath = csvFileUrl.replace(`${baseUrl}/`, "");
  const filePath = path.join(__dirname, "../public", relativePath);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "CSV file not found." });
  }

  const contacts = [];
  const ignoredContacts = [];
  const startTime = Date.now();

  try {
    const readStream = fs.createReadStream(filePath).pipe(csv());

    for await (const row of readStream) {
      const mappedContact = mapCsvRow(
        row,
        mapping,
        userId,
        detectedVariables || {},
        manualVariables || {}
      );

      if (!mappedContact.businessEmail) {
        ignoredContacts.push({ ...row, reason: "Missing business email" });
        continue;
      }

      if (!mappedContact.fullName) {
        mappedContact.fullName =
          generateFullName(mappedContact) ||
          mappedContact.businessEmail.split("@")[0];
      }

      const exists = await Contact.exists({
        businessEmail: mappedContact.businessEmail,
      });
      if (exists) {
        ignoredContacts.push({ ...row, reason: "Email already exists" });
      } else {
        contacts.push(mappedContact);
      }
    }

    const { insertedCount, errors } = await insertContacts(
      contacts,
      ignoredContacts
    );

    const durationToImport = (Date.now() - startTime) / 1000;

    const importHistory = await new ImportHistory({
      importedFileName: path.basename(filePath),
      importedFileURL: csvFileUrl,
      importedBy: userId,
      importType: "contact",
      totalImported: insertedCount,
      totalIgnored: ignoredContacts.length,
      ignoredFileURL: ignoredContacts.length ? csvFileUrl : "",
      durationToImport,
      status:
        insertedCount === contacts.length
          ? "completed"
          : insertedCount > 0
          ? "partial"
          : "failed",
    }).save();

    return res.status(200).json({
      message: `${insertedCount} contacts imported, ${ignoredContacts.length} ignored.`,
      totalImported: insertedCount,
      totalIgnored: ignoredContacts.length,
      ignoredContacts,
      importErrors: errors.length ? errors : undefined,
      status: importHistory.status,
    });
  } catch (err) {
    console.error("Import error:", err);
    return res.status(500).json({ error: err.message });
  }
};



function mapCsvRow(
  row,
  mapping,
  userId,
  detectedVariables = {},
  manualVariables = {}
) {
  const contact = {
    creatorId: userId,
    src: "csv import",
    variables: new Map(),
  };

  // Helper to sanitize all values in an object
  const sanitizeObject = (obj) => {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = sanitizer.sanitize(value);
    }
    return result;
  };

  // Sanitize variable names
  const safeDetectedVariables = sanitizeObject(detectedVariables);
  const safeManualVariables = sanitizeObject(manualVariables);

  // Map standard fields from CSV row to contact fields
  for (const [csvKey, fieldKey] of Object.entries(mapping)) {
    const rawValue = row[csvKey];
    if (!rawValue) continue;

    const value = sanitizer.sanitize(rawValue);

    switch (fieldKey) {
      case "birthday":
        contact.birthday = parseDate(value);
        break;
       case "referral":
        contact.referral = ["true", "yes", "1","0"].includes(value.toLowerCase());
        break;
      case "tags":
        // Append tags instead of overwriting
        if (!contact.tags) contact.tags = [];
        const newTags = value.split(",").map((tag) => tag.trim()).filter(tag => tag);
        contact.tags = [...contact.tags, ...newTags];
        break;
      case "notes":
        // Append notes instead of overwriting
        if (!contact.notes) contact.notes = [];
        const newNotes = value.split("\n").map((note) => note.trim()).filter(note => note);
       
        contact.notes = [...contact.notes, ...newNotes];
        console.log(`Added notes from ${csvKey}:`, 'Total notes:', contact.notes);
        break;
      default:
        contact[fieldKey] = value;
    }
  }

  // Assign detected variables from the CSV
  for (const [csvKey, variableName] of Object.entries(safeDetectedVariables)) {
    if (row[csvKey]) {
      const value = sanitizer.sanitize(row[csvKey]);
      contact.variables.set(variableName, value);
    }
  }

  // Assign manual variables
  for (const [variableName, value] of Object.entries(safeManualVariables)) {
    contact.variables.set(variableName, value);
  }

  return contact;
}

function parseDate(value) {
  const parts = value.split(/[-/]/);
  if (value.match(/^\d{4}-\d{2}-\d{2}$/)) return new Date(value);

  if (parts.length === 3) {
    const [a, b, c] = parts.map(Number);
    const [day, month, year] = a <= 12 ? [b, a - 1, c] : [a, b - 1, c];
    const date = new Date(year, month, day);
    return isNaN(date.getTime()) ? undefined : date;
  }

  const date = new Date(value);
  return isNaN(date.getTime()) ? undefined : date;
}

function generateFullName({ firstName, lastName }) {
  return [firstName, lastName].filter(Boolean).join(" ");
}

async function insertContacts(contacts, ignoredContacts) {
  let insertedCount = 0;
  const errors = [];

  if (!contacts.length) return { insertedCount, errors };

  try {
    const result = await Contact.insertMany(contacts, { ordered: false });
    insertedCount = result.length;
  } catch (bulkError) {
    if (bulkError.writeErrors) {
      bulkError.writeErrors.forEach((err) => {
        const failedContact = contacts[err.index];
        ignoredContacts.push({
          ...failedContact,
          reason: `Validation error: ${
            err.err?.errmsg || err.err?.message || "Unknown"
          }`,
        });
        errors.push({
          index: err.index,
          contact: failedContact.businessEmail,
          error: err.err?.errmsg || err.err?.message || "Unknown",
        });
      });
      insertedCount = contacts.length - bulkError.writeErrors.length;
    } else {
      throw bulkError;
    }
  }

  return { insertedCount, errors };
}

//import contact without saving history
exports.importContactsOld = async (req, res) => {
  const { csvFileUrl } = req.body;

  if (!csvFileUrl) {
    return res.status(400).json({ error: "No CSV file URL provided" });
  }

  // Construct the local file path from the URL
  const baseUrl = `${req.protocol}://${req.get("host")}`;
  const relativeFilePath = csvFileUrl.replace(`${baseUrl}/`, "");
  const filePath = path.join(__dirname, "../public/", relativeFilePath);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "File not found" });
  }

  let contacts = [];
  let ignoredContacts = [];

  const readStream = fs.createReadStream(filePath).pipe(csv());

  for await (const row of readStream) {
    // Add the creatorId to each contact
    row.creatorId = req.user._id;
    row.src = "csv import";

    // Check if the email already exists in the database
    const existingContact = await Contact.findOne({
      businessEmail: row.businessEmail,
    });

    if (existingContact) {
      // Add the contact to ignoredContacts with a reason
      ignoredContacts.push({
        ...row,
        reason: "Email already exists in the database",
      });
    } else {
      contacts.push(row);
    }
  }

  try {
    if (contacts.length > 0) {
      // Save non-duplicate contacts to the database
      const result = await Contact.insertMany(contacts);

      // Clean up uploaded file if needed
      fs.unlinkSync(filePath);

      return res.status(200).json({
        message: `${result.length} Contacts imported, ${ignoredContacts.length} Contacts ignored`,
        totalImported: result.length,
        totalIgnored: ignoredContacts.length,
        ignoredContacts,
      });
    } else {
      // No new contacts to import, just return the ignored contacts
      return res.status(200).json({
        message: `No new contacts imported, ${ignoredContacts.length} Contacts ignored`,
        totalImported: 0,
        totalIgnored: ignoredContacts.length,
        ignoredContacts,
      });
    }
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Bulk add contacts from CSV (not used)
exports.bulkAddContacts = async (req, res) => {
  if (!req.files || !req.files.file) {
    return res.status(400).json({ error: "No file uploaded" });
  }

  const file = req.files.file;
  const results = [];
  const filePath = path.join(
    __dirname,
    "../mixcertificate-content/uploads/",
    file.name
  );

  // Save the uploaded file to the server
  file.mv(filePath, (err) => {
    if (err) {
      return res.status(500).json({ error: "Failed to save file" });
    }

    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => results.push(data))
      .on("end", async () => {
        try {
          const contacts = await Contact.insertMany(results);
          res.status(201).json(contacts);
        } catch (error) {
          res.status(400).json({ error: error.message });
        } finally {
          fs.unlinkSync(filePath); // Delete the file after processing
        }
      });
  });
};

// Get all contacts (no pagination)
exports.getContactsAll = async (req, res) => {
  try {
    const contacts = await Contact.find();
    const totalContacts = await Contact.countDocuments();

    res.status(200).json({
      totalContacts,
      totalPages: 1, // Since there's no pagination, the total pages will be 1
      currentPage: 1, // Only one page, so current page is 1
      contacts,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get all contacts with pagination
exports.getContacts = async (req, res) => {
  try {
    let {
      page = 1,
      limit = 50,
      search = "",
      orderColumn = "fullName",
      orderDir = "asc",
    } = req.query;
    page = parseInt(page);
    limit = parseInt(limit);

    const query = {}; // Base query

    //  Search in multiple fields
    if (search) {
      // Check if the search term is a number (for array length search)
      if (isNaN(search)) {
        // Search by string fields (fullName, businessEmail)
        query.$or = [
          { fullName: { $regex: search, $options: "i" } },
          { businessEmail: { $regex: search, $options: "i" } },
        ];
      } else {
        // If the search term is a number, search by the length of certificates or badges
        search = Number(search); // Convert the search term to a number
        query.$or = [
          { certificates: { $size: search } }, // Search for documents with certificates array of length `search`
          { badges: { $size: search } }, // Search for documents with badges array of length `search`
        ];
      }
    }

    const sort = {};
    if (orderColumn && orderDir) {
      if (orderColumn === "certificates") {
        // Sort by the length of the certificates array
        sort["certificates.length"] = orderDir === "asc" ? 1 : -1;
      } else if (orderColumn === "badges") {
        // Sort by the length of the badges array
        sort["badges.length"] = orderDir === "asc" ? 1 : -1;
      } else {
        // Default sorting by string field (e.g., fullName)
        sort[orderColumn] = orderDir === "asc" ? 1 : -1;
      }
    }

    //  Fetch paginated, sorted, and filtered contacts
    const contacts = await Contact.find(query)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("certificates")
      .populate("badges");

    //  Get total count (before pagination)
    const totalContacts = await Contact.countDocuments(query);

    res.status(200).json({
      recordsTotal: totalContacts,
      recordsFiltered: totalContacts,
      totalPages: Math.ceil(totalContacts / limit),
      currentPage: page,
      contacts,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get contacts by list of IDs, responded in array, used in contacts list
exports.getContactsByIds = async (req, res) => {
  let { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res
      .status(400)
      .json({ error: "Please provide a valid array of contact IDs" });
  }

  // Sanitize and filter valid ObjectIds
  ids = ids
    .map((id) => sanitizer.sanitize(id))
    .filter((id) => mongoose.Types.ObjectId.isValid(id));

  if (ids.length === 0) {
    return res
      .status(400)
      .json({ error: "No valid contact IDs found after sanitization" });
  }

  try {
    const contacts = await Contact.find({ _id: { $in: ids } });

    res.status(200).json({
      totalContacts: contacts.length,
      contacts,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Search contacts with pagination
exports.searchContacts = async (req, res) => {
  const { page = 1, limit = 10, email } = req.query;
  let searchCriteria = {};

  searchCriteria.businessEmail = { $regex: sanitizer.sanitize(email || ''), $options: "i" };

  try {
    const contacts = await Contact.find(searchCriteria)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    const totalContacts = await Contact.countDocuments(searchCriteria);

    res.status(200).json({
      totalContacts,
      totalPages: Math.ceil(totalContacts / limit),
      currentPage: parseInt(page),
      contacts,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get a single contact by ID
exports.getContactById = async (req, res) => {
  try {
    const rawId = sanitizer.sanitize(req.params.id);

    if (!mongoose.Types.ObjectId.isValid(rawId)) {
      return res.status(400).json({ error: "Invalid contact ID" });
    }
    const contact = await Contact.findById(rawId)
      .populate("badges")
      .populate("certificates");

    if (!contact) {
      return res.status(404).json({ message: "Contact not found" });
    }
    res.status(200).json(contact);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get a single public contact by ID - removed email and other personal data
exports.getPublicContactById = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id)
      .populate("badges")
      .populate("certificates");

    if (!contact) {
      return res.status(404).json({ message: "Contact not found" });
    }

    // Remove the 'businessEmail' field from the contact object
    const { businessEmail, ...contactWithoutEmail } = contact.toObject();

    res.status(200).json(contactWithoutEmail);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

//DATA SENT IN PUBLIC
// Get a single public contact page by ID - removed email and other personal data
exports.getPublicContactPageById = async (req, res) => {
  //console.log("req for single public contac")
  try {
    const contact = await Contact.findById(req.params.id)
      .populate("badges")
      .populate("certificates");

    if (!contact) {
      return res.status(404).json({ message: "Contact not found" });
    }

    // Create dynamic title with full name
    const fullName = contact.fullName || "User";
    const pageTitle = `${fullName} Certificates and Badges`;

    res.render("public-profile", {
      title: pageTitle,
      layout: "layouts/publicPages",
    });
  } catch (error) {
    res
      .status(500)
      .send("Contact not found. It might have been deleted by the owner.");
  }
};

// Update a contact by ID
// UPDATE contact by ID
exports.updateContact = async (req, res) => {
  const rawId = sanitizer.sanitize(req.params.id);
  if (!mongoose.Types.ObjectId.isValid(rawId)) {
    return res.status(400).json({ error: "Invalid contact ID" });
  }

  try {
    console.log("Update Request Body:", req.body);

    // Sanitize and clean communication object
    let finalCommunication = {};
    if (req.body.communication) {
      finalCommunication = Object.fromEntries(
        Object.entries(req.body.communication).filter(
          ([_, value]) => value === true
        )
      );
      delete req.body.communication;
    }

    // Filter out invalid fields
    const updateFields = Object.fromEntries(
      Object.entries(req.body).filter(
        ([_, value]) => value !== null && value !== "" && value !== undefined
      )
    );

    if (Object.keys(finalCommunication).length > 0) {
      updateFields.communication = finalCommunication;
    }

    // Update
    const updatedContact = await Contact.findByIdAndUpdate(
      rawId,
      { $set: updateFields },
      { new: true, runValidators: true }
    );

    if (!updatedContact) {
      return res.status(404).json({ message: "Contact not found" });
    }

    res.status(200).json(updatedContact);
  } catch (error) {
    console.error("Error updating contact:", error);
    res.status(400).json({ error: error.message });
  }
};

// Delete a contact by ID
exports.deleteContact = async (req, res) => {
  const rawId = sanitizer.sanitize(req.params.id);
  if (!mongoose.Types.ObjectId.isValid(rawId)) {
    return res.status(400).json({ error: "Invalid contact ID" });
  }

  try {
    const contact = await Contact.findByIdAndDelete(rawId);
    if (!contact) {
      return res.status(404).json({ message: "Contact not found" });
    }

    res.status(200).json({ message: "Contact deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


// Delete multiple contacts by IDs
exports.deleteMultipleContacts = async (req, res) => {
  //console.log("req recieved for deleting multiple contacts: ")

  try {
    const { contactIds } = req.body; // Expecting an array of contact IDs in the request body

    //console.log("deleting multiple contacts: ",contactIds)

    if (!Array.isArray(contactIds) || contactIds.length === 0) {
      return res
        .status(400)
        .json({ message: "Please provide an array of contact IDs." });
    }

    const result = await Contact.deleteMany({ _id: { $in: contactIds } });

    if (result.deletedCount === 0) {
      return res
        .status(404)
        .json({ message: "No contacts found with the provided IDs." });
    }

    res.status(200).json({
      message: `${result.deletedCount} contacts deleted successfully.`,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Upload CSV file for import
exports.uploadCSVFile = (req, res) => {
  try {
    // Check if file was uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    // Get the uploaded file
    const file = req.files.file;

    // Check file type
    if (!file.name.endsWith(".csv")) {
      return res.status(400).json({ error: "Only CSV files are allowed" });
    }

    // Create uploads directory if it doesn't exist
    const uploadDir = path.join(__dirname, "../public/uploads");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${file.name}`;
    const filePath = path.join(uploadDir, uniqueFilename);

    // Move the file to the uploads directory
    file.mv(filePath, function (err) {
      if (err) {
        return res.status(500).json({ error: err.message });
      }

      // Generate file URL
      const baseUrl = `${req.protocol}://${req.get("host")}`;
      const fileUrl = `${baseUrl}/uploads/${uniqueFilename}`;

      // Return success response with file URL
      return res.status(200).json({
        success: true,
        message: "File uploaded successfully",
        file: {
          originalName: file.name,
          filename: uniqueFilename,
          url: fileUrl,
        },
      });
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    return res.status(500).json({ error: error.message });
  }
};

// Export all contacts to CSV
exports.exportContactsToCSV = async (req, res) => {
  try {
    const contacts = await Contact.find();
    //console.log(contacts)
    const fields = [
      "firstName",
      "lastName",
      "fullName",
      "certificates",
      "badges",
      "jobTitle",
      "companyName",
      "businessEmail",
      "phoneNumber",
      "companySize",
      "serviceInterest",
      "industry",
      "website",
      "leadSource",
      "referral",
      "contactStatus",
      "companyRevenue",
      "accountManager",
      "companyAddress",
      "linkedInProfile",
      "marketingOptOut.email",
      "marketingOptOut.sms",
      "marketingOptOut.phone",
      "marketingOptOut.linkedIn",
      "marketingOptOut.whatsApp",
      "preferredContactMethod",
      "notes",
    ];
    const fieldscrm = [
      "firstName",
      "lastName",
      "fullName",
      "jobTitle",
      "companyName",
      "businessEmail",
      "phoneNumber",
      "companySize",
      "serviceInterest",
      "industry",
      "website",
      "leadSource",
      "referral",
      "contactStatus",
      "companyRevenue",
      "accountManager",
      "companyAddress",
      "linkedInProfile",
      "marketingOptOut.email",
      "marketingOptOut.sms",
      "marketingOptOut.phone",
      "marketingOptOut.linkedIn",
      "marketingOptOut.whatsApp",
      "preferredContactMethod",
      "notes",
    ];

    const opts = { fields };
    const parser = new Parser(opts);
    const csv = parser.parse(contacts);

    // Format the current date and time
    const now = moment().format("YYYY-MM-DD_HH-mm-ss_Z");

    res.header("Content-Type", "text/csv");
    res.attachment("mixcrm_contacts_" + now + ".csv");
    res.status(200).send(csv);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get total count of Contact documents
exports.getContactCount = async (req, res) => {
  try {
    const totalCount = await Contact.countDocuments();
    res.json({ totalCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get estimated count of Contact documents
exports.getEstimatedContactCount = async (req, res) => {
  try {
    const estimatedCount = await Contact.estimatedDocumentCount();
    res.json({ estimatedCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
