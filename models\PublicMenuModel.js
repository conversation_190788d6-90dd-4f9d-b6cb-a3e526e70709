const mongoose = require("mongoose");

const menuItemSchema = new mongoose.Schema({
  text: { type: String, required: true, trim: true }, // Menu item text (required, trimmed)
  url: { type: String, required: true, trim: true }, // Menu item URL (required, trimmed)
  target: {
    type: String,
    enum: ["_blank", "_self", "_parent", "_top"],
    default: "_blank",
  },
});

const publicMenuSchema = new mongoose.Schema(
  {
    items: [menuItemSchema], // Array of menu items
    fontColor: {
      type: String,
      default: "#ffffff",
      validate: {
        validator: function (v) {
          return (
            /^#([0-9A-F]{3}){1,2}$/i.test(v) || // hex
            /^rgb\((\s*\d+\s*,){2}\s*\d+\s*\)$/i.test(v) || // rgb
            /^rgba\((\s*\d+\s*,){3}\s*(0|0?\.\d+|1(\.0)?)\s*\)$/i.test(v) // rgba
          );
        },
        message: (props) => `${props.value} is not a valid color format!`,
      },
    },
    backgroundColor: {
      type: String,
      default: "#5156be",
      validate: {
        validator: function (v) {
          return (
            /^#([0-9A-F]{3}){1,2}$/i.test(v) ||
            /^rgb\((\s*\d+\s*,){2}\s*\d+\s*\)$/i.test(v) ||
            /^rgba\((\s*\d+\s*,){3}\s*(0|0?\.\d+|1(\.0)?)\s*\)$/i.test(v)
          );
        },
        message: (props) => `${props.value} is not a valid color format!`,
      },
    },
    businessId: { type: mongoose.Schema.Types.ObjectId, ref: "Business" },
    logoUrl: {
      type: String,
      default:
        "https://images.teamtailor-cdn.com/images/s3/teamtailor-production/logotype-v3/image_uploads/74cdb517-4222-4868-bbb7-55d16eeb445a/original.png",
    },
    font: {
      fontFamily: { type: String, default: "Arial, sans-serif" },
      fontWeight: { type: String, default: "normal" },
      fontSize: { type: String, default: "16px" },
    },
    favIcon: { type: String },
  },
  { timestamps: true }
);

module.exports = mongoose.model("publicMenu", publicMenuSchema);
