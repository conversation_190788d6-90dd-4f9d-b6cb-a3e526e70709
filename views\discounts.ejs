<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Discounts" }) %> 

<%- contentFor('body') %> <%-include("partials/page-title",{"title": "Discounts" , "pagetitle": "Discounts" }) %>

<div id="layout-wrapper" >
 

  <div class="main-content">
    <div class="page-content">
      <div class="container-fluid">
        <!-- Page Title -->
        <div class="row">
          <div class="col-12">
            <div
              class="page-title-box d-sm-flex align-items-center justify-content-between"
            >
              <h4 class="mb-sm-0 font-size-18">Discount Management</h4>
              <div class="page-title-right">
                <ol class="breadcrumb m-0">
                  <li class="breadcrumb-item">
                    <a href="javascript: void(0);">Dashboard</a>
                  </li>
                  <li class="breadcrumb-item active">Discounts</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Discount Card -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div
                class="card-header"
                style="background-color: #5156be; color: #ffffff"
              >
                <h4 class="card-title mb-0" style="color: #ffffff">
                  <i class="fas fa-plus-circle me-2"></i>Create New Discount
                </h4>
              </div>
              <div class="card-body">
                <form id="createDiscountForm">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="description" class="form-label"
                          >Description <span class="text-danger">*</span></label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="description"
                          name="description"
                          required
                        />
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <label for="type" class="form-label"
                          >Type <span class="text-danger">*</span></label
                        >
                        <select
                          class="form-select"
                          id="type"
                          name="type"
                          required
                        >
                          <option value="">Select Type</option>
                          <option value="percentage">Percentage</option>
                          <option value="fixed">Fixed Amount</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <label for="amount" class="form-label"
                          >Amount <span class="text-danger">*</span></label
                        >
                        <input
                          type="number"
                          class="form-control"
                          id="amount"
                          name="amount"
                          min="0"
                          step="0.01"
                          required
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="expires_at" class="form-label"
                          >Expires At <span class="text-danger">*</span></label
                        >
                        <input
                          type="datetime-local"
                          class="form-control"
                          id="expires_at"
                          name="expires_at"
                          required
                        />
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <div class="form-check form-switch">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            id="enabled_for_checkout"
                            name="enabled_for_checkout"
                            checked
                          />
                          <label
                            class="form-check-label"
                            for="enabled_for_checkout"
                          >
                            Enable for Checkout
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="mb-3">
                        <div class="form-check form-switch">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            id="recur"
                            name="recur"
                          />
                          <label class="form-check-label" for="recur">
                            Recurring Discount
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row" id="recurringOptions" style="display: none">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label
                          for="maximum_recurring_intervals"
                          class="form-label"
                          >Maximum Recurring Intervals</label
                        >
                        <input
                          type="number"
                          class="form-control"
                          id="maximum_recurring_intervals"
                          name="maximum_recurring_intervals"
                          min="0"
                        />
                      
                      </div>
                    </div>
                  </div>
                  <div class="text-end">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      style="
                        background-color: #5156be;
                        border-color: #5156be;
                        color: #ffffff;
                      "
                    >
                      <i class="fas fa-save me-2"></i>Create Discount
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Discounts Table -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div
                class="card-header"
                style="background-color: #5156be; color: #ffffff"
              >
                <h4 class="card-title mb-0" style="color: #ffffff">
                  <i class="fas fa-list me-2"></i>All Discounts
                </h4>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table
                    class="table table-bordered table-striped"
                    id="discountsTable"
                  >
                    <thead style="background-color: #5156be; color: #ffffff">
                      <tr>
                        <th style="color: #ffffff">Code</th>
                        <th style="color: #ffffff">Description</th>
                        <th style="color: #ffffff">Type</th>
                        <th style="color: #ffffff">Amount</th>
                        <th style="color: #ffffff">Status</th>
                        <th style="color: #ffffff">Times Used</th>
                        <th style="color: #ffffff">Expires At</th>
                        <th style="color: #ffffff">Actions</th>
                      </tr>
                    </thead>
                    <tbody id="discountsTableBody">
                      <!-- Discounts will be loaded here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="d-none">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
<style>
  .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .btn-primary:hover {
    background-color: #4a4fb7 !important;
    border-color: #4a4fb7 !important;
  }

  .form-check-input:checked {
    background-color: #5156be;
    border-color: #5156be;
  }

  .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(81, 86, 190, 0.05);
  }

  .badge-success {
    background-color: #28a745;
  }

  .badge-danger {
    background-color: #dc3545;
  }

  .badge-warning {
    background-color: #ffc107;
    color: #212529;
  }

  #loadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .action-btn {
    margin: 0 2px;
    padding: 4px 8px;
    font-size: 12px;
  }
</style>

<%- contentFor('FooterJs') %>

<script>
  $(document).ready(function () {
    // Load discounts on page load
    loadDiscounts();

    // Handle recurring checkbox change
    $("#recur").change(function () {
      if ($(this).is(":checked")) {
        $("#recurringOptions").show();
        $("#maximum_recurring_intervals").attr("required", true);
      } else {
        $("#recurringOptions").hide();
        $("#maximum_recurring_intervals").attr("required", false);
        $("#maximum_recurring_intervals").val("");
      }
    });

    // Handle form submission
    $("#createDiscountForm").submit(function (e) {
      e.preventDefault();
      createDiscount();
    });
  });

  function showLoading() {
    $("#loadingOverlay").removeClass("d-none");
  }

  function hideLoading() {
    $("#loadingOverlay").addClass("d-none");
  }

  function createDiscount() {
    showLoading();

    const formData = {
      description: $("#description").val(),
      type: $("#type").val(),
      amount: parseFloat($("#amount").val()),
      enabled_for_checkout: $("#enabled_for_checkout").is(":checked"),
      recur: $("#recur").is(":checked"),
      maximum_recurring_intervals: $("#maximum_recurring_intervals").val()
        ? parseInt($("#maximum_recurring_intervals").val())
        : null,
      expires_at: $("#expires_at").val(),
    };

    $.ajax({
      url: "/api/discounts",
      method: "POST",
      contentType: "application/json",
      data: JSON.stringify(formData),
      success: function (response) {
        hideLoading();
        showToast("Discount created successfully!", "success");
        $("#createDiscountForm")[0].reset();
        $("#recurringOptions").hide();
        loadDiscounts();
      },
      error: function (xhr) {
        hideLoading();
        const error = xhr.responseJSON?.error || "Failed to create discount";
        showToast(error, "error");
      },
    });
  }

  function loadDiscounts() {
    showLoading();

    $.ajax({
      url: "/api/discounts",
      method: "GET",
      success: function (discounts) {
        hideLoading();
        displayDiscounts(discounts);
      },
      error: function (xhr) {
        hideLoading();
        showToast("Failed to load discounts", "error");
      },
    });
  }

  function displayDiscounts(discounts) {
    const tbody = $("#discountsTableBody");
    tbody.empty();

    if (discounts.length === 0) {
      tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                            No discounts found. Create your first discount above.
                        </td>
                    </tr>
                `);
      return;
    }

    discounts.forEach((discount) => {
      const statusBadge = getStatusBadge(discount.status);
      const typeBadge = getTypeBadge(discount.type);
      const expiresAt = new Date(discount.expires_at).toLocaleDateString();
      const amount =
        discount.type === "percentage"
          ? `${discount.amount}%`
          : `$${discount.amount}`;

      tbody.append(`
                    <tr>
                        <td><strong>${discount.code}</strong></td>
                        <td>${discount.description}</td>
                        <td>${typeBadge}</td>
                        <td>${amount}</td>
                        <td>${statusBadge}</td>
                        <td><span class="badge bg-info">${discount.times_used}</span></td>
                        <td>${expiresAt}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary action-btn" onclick="viewDiscount('${discount._id}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger action-btn" onclick="deleteDiscount('${discount._id}')" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `);
    });
  }

  function getStatusBadge(status) {
    switch (status) {
      case "active":
        return '<span class="badge badge-success">Active</span>';
      case "inactive":
        return '<span class="badge badge-danger">Inactive</span>';
      default:
        return '<span class="badge badge-warning">Unknown</span>';
    }
  }

  function getTypeBadge(type) {
    switch (type) {
      case "percentage":
        return '<span class="badge bg-primary">Percentage</span>';
      case "fixed":
        return '<span class="badge bg-secondary">Fixed Amount</span>';
      default:
        return '<span class="badge bg-warning">Unknown</span>';
    }
  }

  function viewDiscount(discountId) {
    // Implement view discount functionality
    showToast("View discount functionality coming soon!", "info");
  }

  function deleteDiscount(discountId) {
    if (confirm("Are you sure you want to delete this discount?")) {
      // Implement delete functionality
      showToast("Delete discount functionality coming soon!", "info");
    }
  }

  function showToast(message, type) {
    // Implement toast notification
    const alertClass =
      type === "success"
        ? "alert-success"
        : type === "error"
        ? "alert-danger"
        : type === "info"
        ? "alert-info"
        : "alert-warning";

    const toast = $(`
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 10000;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);

    $("body").append(toast);

    setTimeout(() => {
      toast.alert("close");
    }, 5000);
  }
</script>
