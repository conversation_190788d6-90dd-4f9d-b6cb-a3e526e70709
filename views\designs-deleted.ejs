<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title":"Deleted Designs" }) %>

<style>
    .deleted-designs-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .design-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(81, 86, 190, 0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(81, 86, 190, 0.1);
        overflow: hidden;
        position: relative;
    }

    .design-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(81, 86, 190, 0.15);
    }

    .design-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 12px 12px 0 0;
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    }

    .design-card-body {
        padding: 1.5rem;
    }

    .design-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .design-meta {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .design-meta i {
        color: #5156be;
        margin-right: 0.5rem;
    }

    .btn-delete-permanent {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        color: #ffffff;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-delete-permanent:hover {
        background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        color: #ffffff;
    }

    .btn-restore {
        background: linear-gradient(135deg, #5156be 0%, #4c51b8 100%);
        border: none;
        color: #ffffff;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .btn-restore:hover {
        background: linear-gradient(135deg, #4c51b8 0%, #454bb2 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(81, 86, 190, 0.3);
        color: #ffffff;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(81, 86, 190, 0.08);
        margin: 2rem 0;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #5156be;
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }

    .empty-state h3 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: #6c757d;
        font-size: 1.1rem;
        max-width: 400px;
        margin: 0 auto;
    }

    .page-header {
        background: #ffffff;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(81, 86, 190, 0.08);
        border: 1px solid rgba(81, 86, 190, 0.1);
    }

    .page-header h1 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-size: 2rem;
    }

    .page-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .stats-card {
        background: linear-gradient(135deg, #5156be 0%, #4c51b8 100%);
        color: #ffffff;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        font-size: 1rem;
        opacity: 0.9;
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4rem;
    }

    .spinner-border-custom {
        width: 3rem;
        height: 3rem;
        border: 0.3em solid rgba(81, 86, 190, 0.2);
        border-top: 0.3em solid #5156be;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }

    .pagination .page-link {
        color: #5156be;
        border: 1px solid rgba(81, 86, 190, 0.2);
        padding: 0.75rem 1rem;
        margin: 0 0.25rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background-color: #5156be;
        color: #ffffff;
        border-color: #5156be;
    }

    .pagination .page-item.active .page-link {
        background-color: #5156be;
        border-color: #5156be;
        color: #ffffff;
    }

    .deleted-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(220, 53, 69, 0.9);
        color: #ffffff;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
    }

    .bulk-actions {
        background: #ffffff;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(81, 86, 190, 0.05);
        border: 1px solid rgba(81, 86, 190, 0.1);
    }

    .btn-bulk-action {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border: none;
        color: #ffffff;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }

    .btn-bulk-action:hover {
        background: linear-gradient(135deg, #5a6268 0%, #545b62 100%);
        color: #ffffff;
    }
</style>

<%- contentFor('body') %>

<div class="deleted-designs-container">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-trash-alt me-3"></i>Deleted Designs</h1>
                    <p>Manage and permanently delete your removed designs. Deleted designs can be restored or permanently removed.</p>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number" id="totalDeletedCount">0</div>
                        <div class="stats-label">Deleted Designs</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" id="bulkActions" style="display: none;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="fw-bold text-muted">
                        <span id="selectedCount">0</span> design(s) selected
                    </span>
                </div>
                <div class="col-md-6 text-end">
                    <button style="color: #ffffff; background: #5156be;" class="btn btn-bulk-action" id="bulkRestore">
                        <i class="fas fa-undo me-2"></i>Restore Selected
                    </button>
                    <button style="color: #ffffff; background: #dc3545;" class="btn btn-bulk-action" id="bulkDelete">
                        <i class="fas fa-trash me-2"></i>Delete Permanently
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border-custom"></div>
        </div>

        <!-- Designs Grid -->
        <div class="row" id="designsGrid" style="display: none;">
            <!-- Designs will be loaded here -->
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-state-icon">
                <i class="fas fa-trash-alt"></i>
            </div>
            <h3>No Deleted Designs</h3>
            <p>You don't have any deleted designs. When you delete designs, they'll appear here where you can restore or permanently remove them.</p>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <nav aria-label="Deleted designs pagination">
                <ul class="pagination" id="pagination">
                    <!-- Pagination will be generated here -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Restore Confirmation Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1" aria-labelledby="restoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #5156be; color: #ffffff;">
                <h5 style="color: #ffffff" class="modal-title" id="restoreModalLabel">
                    <i class="fas fa-undo me-2"></i>Restore Design
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to restore this design? It will be moved back to your active designs.</p>
                <div class="design-preview" id="restoreDesignPreview">
                    <!-- Design preview will be shown here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button style="color: #ffffff;" type="button" class="btn btn-restore" id="confirmRestore">
                    <i class="fas fa-undo me-2"></i>Restore Design
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #dc3545; color: #ffffff;">
                <h5 style="color: #ffffff" class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Permanent Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone!
                </div>
                <p>Are you sure you want to permanently delete this design? This will remove it completely from your account.</p>
                <div class="design-preview" id="deleteDesignPreview">
                    <!-- Design preview will be shown here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button style="color: #ffffff;" type="button" class="btn btn-delete-permanent" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>Delete Permanently
                </button>
            </div>
        </div>
    </div>
</div>

<%- contentFor('FooterJs') %>
<script>
$(document).ready(function() {
    let currentPage = 1;
    let totalPages = 1;
    let selectedDesigns = [];
    let currentDesignId = null;

    // Load deleted designs
    function loadDeletedDesigns(page = 1) {
        $('#loadingSpinner').show();
        $('#designsGrid').hide();
        $('#emptyState').hide();
        $('#paginationContainer').hide();

        $.ajax({
            url: `/api/design/deleted/get?page=${page}&limit=10`,
            method: 'GET',
            success: function(response) {
                console.log('Deleted designs response:', response);

                if (response.designs && response.designs.length > 0) {
                    renderDesigns(response.designs);
                    updateStats(response.totalDesigns);
                    setupPagination(response.currentPage, response.totalPages, response.totalDesigns);
                    currentPage = response.currentPage;
                    totalPages = response.totalPages;

                    $('#designsGrid').show();
                    $('#paginationContainer').show();
                } else {
                    $('#emptyState').show();
                    updateStats(0);
                }

                $('#loadingSpinner').hide();
            },
            error: function(err) {
                console.error('Error loading deleted designs:', err);
                $('#loadingSpinner').hide();
                showToast('Error loading deleted designs', 'danger');
            }
        });
    }

    // Render designs in grid
    function renderDesigns(designs) {
        const grid = $('#designsGrid');
        grid.empty();

        designs.forEach(function(design) {
            const designCard = createDesignCard(design);
            grid.append(designCard);
        });
    }

    // Create individual design card
    function createDesignCard(design) {
        const createdDate = new Date(design.createdAt).toLocaleDateString();
        const updatedDate = new Date(design.updatedAt).toLocaleDateString();
        const imageUrl = design.imageURL || '/assets/images/design/sample-design-horizontal.jpg';

        return `
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="design-card" data-design-id="${design._id}">
                   

                    
                

                    <img src="${imageUrl}" alt="${design.designName}" class="design-image" onerror="this.src='/assets/images/design/sample-design-horizontal.jpg'">

                    <div class="design-card-body">
                        <h5 class="design-title" title="${design.designName}">${truncateText(design.designName, 50)}</h5>

                        <div class="design-meta">
                            <div class="mb-2">
                                <i class="fas fa-calendar-plus"></i>
                                Created: ${createdDate}
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-calendar-edit"></i>
                                Updated: ${updatedDate}
                            </div>
                            ${design.type ? `<div class="mb-2"><i class="fas fa-tag"></i>Type: ${design.type}</div>` : ''}
                        </div>

                        <div class="d-grid gap-2">
                            <button style="color: #ffffff;" class="btn btn-restore" onclick="showRestoreModal('${design._id}', '${design.designName}', '${imageUrl}')">
                                <i class="fas fa-undo me-2"></i>Restore
                            </button>
                            <button style="color: #ffffff;" class="btn btn-delete-permanent" onclick="showDeleteModal('${design._id}', '${design.designName}', '${imageUrl}')">
                                <i class="fas fa-trash me-2"></i>Delete Permanently
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Update statistics
    function updateStats(totalCount) {
        $('#totalDeletedCount').text(totalCount);
    }

    // Setup pagination
    function setupPagination(currentPage, totalPages, totalItems) {
        const pagination = $('#pagination');
        pagination.empty();

        if (totalPages <= 1) {
            $('#paginationContainer').hide();
            return;
        }

        // Previous button
        pagination.append(`
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})" aria-label="Previous">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `);

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            pagination.append(`<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`);
            if (startPage > 2) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            pagination.append(`
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            pagination.append(`<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`);
        }

        // Next button
        pagination.append(`
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})" aria-label="Next">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `);

        $('#paginationContainer').show();
    }

    // Change page
    window.changePage = function(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
            loadDeletedDesigns(page);
        }
    };

    // Truncate text
    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    // Show restore modal
    window.showRestoreModal = function(designId, designName, imageUrl) {
        currentDesignId = designId;
        $('#restoreDesignPreview').html(`
            <div class="d-flex align-items-center">
                <img src="${imageUrl}" alt="${designName}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                <div>
                    <h6 class="mb-0">${designName}</h6>
                    <small class="text-muted">ID: ${designId}</small>
                </div>
            </div>
        `);
        $('#restoreModal').modal('show');
    };

    // Show delete modal
    window.showDeleteModal = function(designId, designName, imageUrl) {
        currentDesignId = designId;
        $('#deleteDesignPreview').html(`
            <div class="d-flex align-items-center">
                <img src="${imageUrl}" alt="${designName}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                <div>
                    <h6 class="mb-0">${designName}</h6>
                    <small class="text-muted">ID: ${designId}</small>
                </div>
            </div>
        `);
        $('#deleteModal').modal('show');
    };

    // Handle restore confirmation
    $('#confirmRestore').on('click', function() {
        if (!currentDesignId) return;

        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin me-2"></i>Restoring...');
        button.prop('disabled', true);

        $.ajax({
            url: `/api/design/${currentDesignId}/update-isdeleted-restore`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({ isDeleted: false }),
            success: function(response) {
                $('#restoreModal').modal('hide');
                showToast('Design restored successfully!', 'success');
                loadDeletedDesigns(currentPage);
            },
            error: function(err) {
                console.error('Error restoring design:', err);
                showToast('Error restoring design', 'danger');
            },
            complete: function() {
                button.html(originalText);
                button.prop('disabled', false);
            }
        });
    });

    // Handle permanent delete confirmation
    $('#confirmDelete').on('click', function() {
        if (!currentDesignId) return;

        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin me-2"></i>Deleting...');
        button.prop('disabled', true);

        $.ajax({
            url: `/api/design/${currentDesignId}`,
            method: 'DELETE',
            success: function(response) {
                $('#deleteModal').modal('hide');
                showToast('Design permanently deleted!', 'success');
                loadDeletedDesigns(currentPage);
            },
            error: function(err) {
                console.error('Error deleting design:', err);
                showToast('Error deleting design permanently', 'danger');
            },
            complete: function() {
                button.html(originalText);
                button.prop('disabled', false);
            }
        });
    });

    // Handle checkbox selection
    $(document).on('change', '.design-checkbox', function() {
        const designId = $(this).val();
        const isChecked = $(this).is(':checked');

        if (isChecked) {
            selectedDesigns.push(designId);
        } else {
            selectedDesigns = selectedDesigns.filter(id => id !== designId);
        }

        updateBulkActions();
    });

    // Update bulk actions visibility
    function updateBulkActions() {
        const count = selectedDesigns.length;
        $('#selectedCount').text(count);

        if (count > 0) {
            $('#bulkActions').show();
        } else {
            $('#bulkActions').hide();
        }
    }

   
   

    // Initialize page
    loadDeletedDesigns(1);
});
</script>

<!-- <div class="form-check position-absolute" style="top: 1rem; left: 1rem; z-index: 10;">
                        <input class="form-check-input design-checkbox" type="checkbox" value="${design._id}" id="design_${design._id}">
                     </div> -->