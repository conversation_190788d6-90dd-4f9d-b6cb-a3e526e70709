<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Deleted Designs" }) %> 


<%- contentFor('body') %>
<%-include("partials/page-title",{"title": "Deleted Designs", "pagetitle": "MixCertificate" }) %>


<div>

    <!-- content goes here  -->
</div>
<%- contentFor('FooterJs') %>

<script>
    $(document).ready(function(){
        let page = 1;
        let limit = 10;
        $.ajax({
            url: `/api/design/deleted/get?page=${page}&limit=${limit}`,
            method: 'GET',
            success: function(response){
                console.log(response);
            },
            error: function(err){
                console.log(err);
                
            }
        });

        $(document).on('click', '.delete-design', function () {
            let designId = $(this).data('id');
            if (confirm('Are you sure you want to delete this design?')) {
                deleteDesign(designId);
            }
        });

        $.ajax({
            url: `/api/design/${designId}`,
            method: 'DELETE',
            success: function (response) {
                console.log('Design deleted successfully:', response.message);
                location.reload(); // Reload the page or update the UI dynamically
            },
            error: function (err) {
                console.log('Error deleting design:', err);
            }
        });
    })
</script>