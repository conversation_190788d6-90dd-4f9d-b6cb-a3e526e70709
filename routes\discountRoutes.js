const express=require('express');
const router=express.Router();
const discountController=require('../controllers/discountController');
const { checkPermissions } = require('../middleware/middleware');

// Create a new discount
router.post('/', checkPermissions('Discount', ['create']), discountController.createDiscount);

// Get all discounts
router.get('/', checkPermissions('Discount', ['read']), discountController.getDiscounts);
router.put('/update-should-be-applied/:id', checkPermissions('Discount', ['update']), discountController.updateDiscountShouldBeApplied);


module.exports = router;


