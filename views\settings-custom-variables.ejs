<%- contentFor('HeaderCss') %> <%-include("partials/title-meta",{"title":"Custom Variables Settings" }) %>
<style>
  .custom-variables-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .custom-variables-table table {
    table-layout: fixed;
    width: 100%;
  }

  .custom-variables-table thead {
    background-color: #5156be;
    color: #ffffff;
  }

  .custom-variables-table th {
    font-weight: 500;
    padding: 15px 20px;
    font-size: 14px;
    letter-spacing: 0.3px;
    text-transform: uppercase;
  }

  .custom-variables-table td {
    padding: 15px 20px;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .custom-variables-table td:first-child {
    max-width: 200px;
  }

  .custom-variables-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
  }

  .custom-variables-table tbody tr:last-child {
    border-bottom: none;
  }

  .custom-variables-table tbody tr:hover {
    background-color: rgba(81, 86, 190, 0.05);
    transform: translateY(-1px);
  }

  .action-btn {
    width: 34px;
    height: 34px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
  }

  .action-btn:hover {
    background-color: #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  .action-btn i {
    font-size: 16px;
  }

  .action-btn.edit-variable:hover {
    border-color: rgba(81, 86, 190, 0.3);
  }

  .action-btn.delete-variable:hover {
    border-color: rgba(220, 53, 69, 0.3);
  }

  .btn-primary {
    background-color: #5156be;
    border-color: #5156be;
  }

  .btn-primary:hover,
  .btn-primary:focus,
  .btn-primary:active {
    background-color: #4347a1;
    border-color: #4347a1;
  }

  .modal-header {
    background-color: #5156be;
    color: #ffffff;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
  }

  .empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #5156be;
    opacity: 0.5;
  }

  .variable-badge {
    background-color: rgba(81, 86, 190, 0.1);
    color: #5156be;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    letter-spacing: 0.5px;
    display: inline-block;
    text-align: center;
    min-width: 80px;
    white-space: nowrap;
  }

  .variable-badge.string {
    background-color: rgba(81, 86, 190, 0.1);
    color: #5156be;
  }

  .variable-badge.number {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  .variable-badge.boolean {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  .variable-badge.date {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  .variable-badge.email {
    background-color: rgba(255, 193, 7, 0.1);
    color: #fd7e14;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 8px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(81, 86, 190, 0.2);
    border-radius: 50%;
    border-top-color: #5156be;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Button spinner styles */
  .btn-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.15rem solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 0.75s linear infinite;
    margin-right: 0.5rem;
    vertical-align: text-bottom;
  }

  .btn-light .btn-spinner {
    border: 0.15rem solid rgba(81, 86, 190, 0.2);
    border-top-color: #5156be;
  }

  .btn-danger .btn-spinner {
    border: 0.15rem solid rgba(255, 255, 255, 0.5);
    border-top-color: #fff;
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title", {"title": "Custom Variables Settings" , "pagetitle": "Settings" }) %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body position-relative">
          <!-- Loading Overlay -->
          <div id="loadingOverlay" class="loading-overlay">
            <div class="spinner"></div>
          </div>

          <div class="d-flex justify-content-between align-items-center mb-2">
            <h4 class="card-title mb-0">Custom Variables</h4>
            <a
              href="https://www.mixcommerce.co/mixcertificate/documentation"
              target="_blank"
              class="text-decoration-none"
            >
              <i class="bx bx-question-mark me-1"></i> How to use?
            </a>
          </div>

          <!-- Pre-generated Variables Section -->
          <div class="mb-4">
            <div class="alert alert-light border">
              <h5 class="mb-3">
                <i class="bx bx-info-circle text-primary me-2"></i>Pre-generated
                Variables
              </h5>
              <p class="text-muted mb-3">
                These variables are automatically generated by the system and
                available for use in your templates. No need to create them
                manually.
              </p>

              <div class="table-responsive">
                <table class="table table-bordered table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th style="width: 30%">Variable Name</th>
                      <th style="width: 20%">Data Type</th>
                      <th style="width: 50%">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>fullName</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Contact's full name</td>
                    </tr>
                    <tr>
                      <td><code>businessEmail</code></td>
                      <td><span class="variable-badge email">email</span></td>
                      <td>Contact's business email address</td>
                    </tr>
                    <tr>
                      <td><code>jobTitle</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Contact's job title or position</td>
                    </tr>
                    <tr>
                      <td><code>companyName</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Name of the contact's company or organization</td>
                    </tr>
                    <tr>
                      <td><code>phoneNumber</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Contact's phone number</td>
                    </tr>
                    <tr>
                      <td><code>contactStatus</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>
                        Current status of the contact (e.g., Active, Inactive)
                      </td>
                    </tr>
                    <tr>
                      <td><code>leadSource</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Source from which the contact was acquired</td>
                    </tr>
                    <tr>
                      <td><code>preferredContactMethod</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Contact's preferred method of communication</td>
                    </tr>
                    <tr>
                      <td><code>notes</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Additional notes about the contact</td>
                    </tr>
                    <tr>
                      <td><code>tags</code></td>
                      <td><span class="variable-badge string">string</span></td>
                      <td>Tags associated with the contact</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-between align-items-center mb-4">
            <p class="text-muted mb-0">
              Create and manage custom variables for your certificates and
              templates.
            </p>
            <button
              type="button"
              class="btn btn-primary"
              data-bs-toggle="modal"
              data-bs-target="#addVariableModal"
            >
              <i class="bx bx-plus me-1"></i> Add Variable
            </button>
          </div>

          <div class="table-responsive custom-variables-table">
            <table class="table table-borderless mb-0">
              <thead>
                <tr>
                  <th style="width: 35%">Variable Name</th>
                  <th style="width: 25%">Data Type</th>
                  <th style="width: 25%">Created By</th>
                  <th style="width: 15%" class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody id="variablesTableBody">
                <!-- Table content will be loaded dynamically -->
              </tbody>
            </table>
          </div>

          <!-- Empty state (will be shown when no variables exist) -->
          <div id="emptyState" class="empty-state d-none">
            <i class="bx bx-code-curly"></i>
            <h5>No Custom Variables Yet</h5>
            <p>
              Create your first custom variable by clicking the "Add Variable"
              button above.
            </p>
          </div>

          <!-- AI Suggested Variables Section -->
          <div class="mt-5" id="aiSuggestedVariables">
            <h5 class="border-bottom pb-2 mb-3">
              <i class="bx bx-bulb text-warning me-2"></i>AI Suggested Variables
            </h5>
            <p class="text-muted mb-3">
              Here are some common variables you might want to use in your
              templates:
            </p>

            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead class="table-light">
                  <tr>
                    <th style="width: 30%">Variable Name</th>
                    <th style="width: 20%">Data Type</th>
                    <th style="width: 50%">Description</th>
                  </tr>
                </thead>
                <tbody id="suggestedVariablesTableBody">
                  <!-- This will be populated by JavaScript -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Variable Modal -->
<div
  class="modal fade"
  id="addVariableModal"
  tabindex="-1"
  aria-labelledby="addVariableModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5
          style="color: #ffffff"
          class="modal-title"
          id="addVariableModalLabel"
        >
          Add Custom Variable
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="addVariableForm">
          <div class="mb-3">
            <label for="variableName" class="form-label">Variable Name</label>
            <input
              type="text"
              class="form-control"
              id="variableName"
              placeholder="Enter variable name"
              required
            />
            <div class="form-text">
              Use a descriptive name without spaces (e.g., firstName, userEmail)
            </div>
            <div class="invalid-feedback" id="variableNameFeedback">
              A variable with this name already exists.
            </div>
          </div>
          <div class="mb-3">
            <label for="variableType" class="form-label">Data Type</label>
            <select class="form-select" id="variableType" required>
              <option value="" selected disabled>Select data type</option>
              <option value="string">String</option>
              <option value="number">Number</option>
              <option value="boolean">Boolean</option>
              <option value="date">Date</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="saveVariableBtn">
          <span class="btn-spinner d-none"></span>
          <span class="btn-text">Save Variable</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Variable Modal -->
<div
  class="modal fade"
  id="editVariableModal"
  tabindex="-1"
  aria-labelledby="editVariableModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5
          style="color: #ffffff"
          class="modal-title"
          id="editVariableModalLabel"
        >
          Edit Custom Variable
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="editVariableForm">
          <input type="hidden" id="editVariableNameOriginal" />
          <div class="mb-3">
            <label for="editVariableName" class="form-label"
              >Variable Name</label
            >
            <input
              type="text"
              class="form-control"
              id="editVariableName"
              placeholder="Enter variable name"
              required
            />
            <div class="form-text">
              Use a descriptive name without spaces (e.g., firstName, userEmail)
            </div>
            <div class="invalid-feedback" id="editVariableNameFeedback">
              A variable with this name already exists.
            </div>
          </div>
          <div class="mb-3">
            <label for="editVariableType" class="form-label">Data Type</label>
            <select class="form-select" id="editVariableType" required>
              <option value="" selected disabled>Select data type</option>
              <option value="string">String</option>
              <option value="number">Number</option>
              <option value="boolean">Boolean</option>
              <option value="date">Date</option>
              <option value="email">Email</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="updateVariableBtn">
          <span class="btn-spinner d-none"></span>
          <span class="btn-text">Update Variable</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div
  class="modal fade"
  id="deleteVariableModal"
  tabindex="-1"
  aria-labelledby="deleteVariableModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5
          style="color: #ffffff"
          class="modal-title"
          id="deleteVariableModalLabel"
        >
          Confirm Delete
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          Are you sure you want to delete this variable? This action cannot be
          undone.
        </p>
        <p class="mb-0">
          <strong>Variable: </strong><span id="deleteVariableName"></span>
        </p>
        <input type="hidden" id="deleteVariableId" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span class="btn-spinner d-none"></span>
          <span class="btn-text">Delete</span>
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<script>
  $(document).ready(function () {
    const predefinedVariables = {
      fullName: "string",
      businessEmail: "email",
      jobTitle: "string",
      companyName: "string",
      phoneNumber: "string",
      contactStatus: "string",
      leadSource: "string",
      preferredContactMethod: "string",
      notes: "string",
      tags: "string",
    };
    // Initialize variables object
    let variables = {};
    let creatorName = "";

    // Show loading indicator initially
    function showLoading() {
      $("#loadingOverlay").show();
    }

    function hideLoading() {
      $("#loadingOverlay").fadeOut(300);
    }

    // Define addVariable function in global scope
    window.addVariable = function () {
      // Show loading indicator
      showLoading();

      const predefinedVariableskey = Object.keys(predefinedVariables);
      const variablesKeys = Object.keys(variables).filter((key) =>
        predefinedVariableskey.includes(key)
      );
      if (variablesKeys.length > 0) {
        showToast("Cannot add Predefined Variables ", "danger");
        hideLoading();
        setButtonLoading("#saveVariableBtn", false);
        setButtonLoading("#updateVariableBtn", false);
        return;
      }

      $.ajax({
        url: "/api/custom-variable/create-update",
        method: "POST",
        data: JSON.stringify({
          contactCustomVariable: variables,
        }),
        contentType: "application/json",
        success: function (response) {
          variables = response.contactCustomVariable;

          fetchCustomVariables();

          // Reset form and close modals
          $("#addVariableForm")[0].reset();
          $("#addVariableModal").modal("hide");
          $("#editVariableModal").modal("hide");

          // Reset button states
          setButtonLoading("#saveVariableBtn", false);
          setButtonLoading("#updateVariableBtn", false);

          showToast("Variable saved successfully", "success");
          hideLoading();
        },
        error: function (error) {
          // Handle error
          showToast("Failed to save variable", "error");

          // Reset button states
          setButtonLoading("#saveVariableBtn", false);
          setButtonLoading("#updateVariableBtn", false);

          hideLoading();
        },
        complete: function () {
          // Ensure loading indicator is hidden in all cases
          setTimeout(hideLoading, 300);
        },
      });
    };

    // Function to render variables table
    function renderVariablesTable() {
      const tableBody = $("#variablesTableBody");
      tableBody.empty();

      if (Object.keys(variables).length === 0) {
        $("#emptyState").removeClass("d-none");
        $(".custom-variables-table").addClass("d-none");
      } else {
        $("#emptyState").addClass("d-none");
        $(".custom-variables-table").removeClass("d-none");
        for (const [name, type] of Object.entries(variables)) {
          tableBody.append(`
                        <tr data-id="${name}">
                            <td title="${name}">
                                <span class="fw-medium">${name}</span>
                            </td>
                            <td>
                                <span class="variable-badge ${type}">${type}</span>
                            </td>
                            <td title="${creatorName}">
                                <span class="text-muted">${
                                  creatorName || "System"
                                }</span>
                            </td>
                            <td>
                                <div class="d-flex justify-content-center">
                                    <button type="button" class="btn btn-light action-btn edit-variable d-flex align-items-center me-2" data-name="${name}">
                                        <i class="bx bx-edit-alt text-primary"></i>
                                    </button>
                                    <button type="button" class="btn btn-light action-btn delete-variable d-flex align-items-center" data-name="${name}">
                                        <i class="bx bx-trash text-danger"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `);
        }
      }
    }

    // Helper functions for button loading state
    function setButtonLoading(buttonId, isLoading) {
      const $button = $(buttonId);
      const $spinner = $button.find(".btn-spinner");
      const $text = $button.find(".btn-text");

      if (isLoading) {
        $spinner.removeClass("d-none");
        $button.prop("disabled", true);
      } else {
        $spinner.addClass("d-none");
        $button.prop("disabled", false);
      }
    }

    // Real-time validation for variable name
    $("#variableName").on("input", function () {
      const name = $(this).val().trim();
      const nameInput = $(this);

      // Reset validation state
      nameInput.removeClass("is-invalid");

      // Check if name is not empty and already exists
      if (name && variables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "A variable with this name already exists."
        );
      }

      // Check if name is a predefined variable
      if (name && predefinedVariables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "Cannot use predefined variable names."
        );
      }

      // Check for invalid characters (spaces, special chars except underscore)
      if (name && !/^[a-zA-Z0-9_]+$/.test(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "Variable names should only contain letters, numbers, and underscores."
        );
      }
    });

    // Add new variable
    $("#saveVariableBtn").on("click", function () {
      const name = $("#variableName").val().trim();
      const type = $("#variableType").val();
      const nameInput = $("#variableName");

      if (!name || !type) {
        showToast("Please enter both variable name and type", "warning");
        return;
      }

      // Validate variable name format
      if (!/^[a-zA-Z0-9_]+$/.test(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "Variable names should only contain letters, numbers, and underscores."
        );
        return;
      }

      // Check if variable name already exists
      if (variables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "A variable with this name already exists."
        );
        return;
      }

      // Check if variable name is a predefined variable
      if (predefinedVariables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#variableNameFeedback").text(
          "Cannot use predefined variable names."
        );
        showToast("Cannot use predefined variable names", "danger");
        return;
      }

      // Show loading state on button
      setButtonLoading("#saveVariableBtn", true);

      // Add to variables object
      variables[name] = type;

      // Call the global addVariable function
      window.addVariable();
    });

    // Real-time validation for edit variable name
    $("#editVariableName").on("input", function () {
      const name = $(this).val().trim();
      const originalName = $("#editVariableNameOriginal").val();
      const nameInput = $(this);

      // Reset validation state
      nameInput.removeClass("is-invalid");

      // Check if name is not empty, has changed, and already exists
      if (name && name !== originalName && variables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "A variable with this name already exists."
        );
      }

      // Check if name is a predefined variable
      if (
        name &&
        name !== originalName &&
        predefinedVariables.hasOwnProperty(name)
      ) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "Cannot use predefined variable names."
        );
      }

      // Check for invalid characters (spaces, special chars except underscore)
      if (name && !/^[a-zA-Z0-9_]+$/.test(name)) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "Variable names should only contain letters, numbers, and underscores."
        );
      }
    });

    // Edit variable - open modal with data
    $(document).on("click", ".edit-variable", function () {
      const variableNameOriginal = $(this).data("name");

      $("#editVariableNameOriginal").val(variableNameOriginal);
      if (variableNameOriginal) {
        // Reset validation state
        $("#editVariableName").removeClass("is-invalid");

        $("#editVariableName").val(variableNameOriginal);
        $("#editVariableType").val(variables[variableNameOriginal]);
        $("#editVariableModal").modal("show");
      }
    });

    // Update variable
    $("#updateVariableBtn").on("click", function () {
      const originalName = $("#editVariableNameOriginal").val();
      const name = $("#editVariableName").val().trim();
      const type = $("#editVariableType").val();
      const nameInput = $("#editVariableName");

      if (!name || !type) {
        showToast("Please enter both variable name and type", "warning");
        return;
      }

      // Validate variable name format
      if (!/^[a-zA-Z0-9_]+$/.test(name)) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "Variable names should only contain letters, numbers, and underscores."
        );
        return;
      }

      // Check if new name already exists (but only if name has changed)
      if (originalName !== name && variables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "A variable with this name already exists."
        );
        return;
      }

      // Check if the new name is a predefined variable
      if (originalName !== name && predefinedVariables.hasOwnProperty(name)) {
        nameInput.addClass("is-invalid");
        $("#editVariableNameFeedback").text(
          "Cannot use predefined variable names."
        );
        showToast("Cannot use predefined variable names", "danger");
        return;
      }

      // Show loading state on button
      setButtonLoading("#updateVariableBtn", true);

      // If name has changed, delete the old key and create a new one
      if (originalName !== name) {
        delete variables[originalName];
      }

      // Set the variable with new name and type
      variables[name] = type;

      // Call the global addVariable function
      window.addVariable();
    });

    // Delete variable - open confirmation modal
    $(document).on("click", ".delete-variable", function () {
      const variableName = $(this).data("name");

      if (variableName) {
        $("#deleteVariableName").text(variableName);
        $("#deleteVariableModal").modal("show");
      }
    });

    // Confirm delete variable
    $("#confirmDeleteBtn").on("click", function () {
      const variableName = $("#deleteVariableName").text();

      // Show loading state on button
      setButtonLoading("#confirmDeleteBtn", true);

      // Show loading indicator
      showLoading();

      $.ajax({
        url: `/api/custom-variable/delete`,
        data: JSON.stringify({ key: variableName }),
        method: "DELETE",
        contentType: "application/json",
        success: function (response) {
          // Close modal first
          $("#deleteVariableModal").modal("hide");

          // Then fetch updated data
          fetchCustomVariables();

          // Show success message
          showToast("Variable deleted successfully", "success");

          // Reset button state (though modal will be hidden)
          setButtonLoading("#confirmDeleteBtn", false);
        },
        error: function (error) {
          // Handle error
          showToast("Failed to delete variable", "error");
          hideLoading();

          // Reset button state
          setButtonLoading("#confirmDeleteBtn", false);
        },
        complete: function () {
          // Ensure loading indicator is hidden in all cases
          setTimeout(hideLoading, 300);
        },
      });
    });

    // AI-suggested variables data
    const suggestedVariables = [
      {
        name: "userName",
        type: "string",
        description: "Full name of the user or recipient",
      },
      {
        name: "userEmail",
        type: "string",
        description: "Email address of the user or recipient",
      },
      {
        name: "certificateTitle",
        type: "string",
        description: "Title of the certificate or achievement",
      },
      {
        name: "completionDate",
        type: "date",
        description: "Date when the certificate was issued or completed",
      },
      {
        name: "expirationDate",
        type: "date",
        description: "Date when the certificate expires (if applicable)",
      },
      {
        name: "courseTitle",
        type: "string",
        description: "Title of the course or program completed",
      },
      {
        name: "courseDuration",
        type: "string",
        description: 'Duration of the course (e.g., "40 hours")',
      },
      {
        name: "instructorName",
        type: "string",
        description: "Name of the instructor or trainer",
      },
      {
        name: "companyName",
        type: "string",
        description: "Name of the issuing company or organization",
      },
      {
        name: "certificateId",
        type: "string",
        description: "Unique identifier for the certificate",
      },
      {
        name: "achievementLevel",
        type: "string",
        description: 'Level of achievement (e.g., "Advanced", "Beginner")',
      },
      {
        name: "score",
        type: "number",
        description: "Numerical score or grade achieved",
      },
      {
        name: "isVerified",
        type: "boolean",
        description: "Whether the certificate has been verified",
      },
    ];

    // Function to render AI-suggested variables
    function renderSuggestedVariables() {
      const tableBody = $("#suggestedVariablesTableBody");
      tableBody.empty();

      // Filter out variables that already exist in user's variables
      const filteredSuggestions = suggestedVariables.filter(
        (suggestion) => !variables.hasOwnProperty(suggestion.name)
      );

      if (filteredSuggestions.length === 0) {
        // If all suggested variables already exist, show a message
        tableBody.append(`
          <tr>
            <td colspan="3" class="text-center py-3">
              <i class="bx bx-check-circle text-success me-2"></i>
              You've already added all our suggested variables!
            </td>
          </tr>
        `);
        return;
      }

      // Add each suggested variable to the table
      filteredSuggestions.forEach((variable) => {
        tableBody.append(`
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <span class="fw-medium">${variable.name}</span>
                <button class="btn btn-sm btn-light ms-2 add-suggested-variable"
                  data-name="${variable.name}"
                  data-type="${variable.type}"
                  title="Add this variable">
                  <i class="bx bx-plus text-primary"></i>
                </button>
              </div>
            </td>
            <td>
              <span class="variable-badge ${variable.type}">${variable.type}</span>
            </td>
            <td>${variable.description}</td>
          </tr>
        `);
      });
    }

    // Add click handler for suggested variables
    $(document).on("click", ".add-suggested-variable", function () {
      const name = $(this).data("name");
      const type = $(this).data("type");

      // Check if variable already exists
      if (variables.hasOwnProperty(name)) {
        showToast(`Variable '${name}' already exists`, "warning");
        return;
      }

      // Add the variable
      variables[name] = type;

      // Save the variable
      window.addVariable();

      // Show toast
      showToast(`Variable '${name}' added successfully`, "success");

      // Update the suggested variables table
      renderSuggestedVariables();
    });

    function fetchCustomVariables() {
      // Show loading indicator
      showLoading();

      $.ajax({
        url: "/api/custom-variable/get",
        method: "GET",
        success: function (response) {
          console.log(response, "res");
          const name =
            response &&
            response.createdBy &&
            response.createdBy.firstName &&
            response.createdBy.lastName
              ? response.createdBy.firstName + " " + response.createdBy.lastName
              : 'System';
          creatorName = name;

          variables = response.contactCustomVariable;
          renderVariablesTable();
          // Render suggested variables after loading user variables
          renderSuggestedVariables();
          // Hide loading indicator
          hideLoading();
        },
        error: function (error) {
          showToast(error.responseJSON.error, "danger");
          renderVariablesTable();
          renderSuggestedVariables();
          // Hide loading indicator even on error
          hideLoading();
        },
        complete: function () {
          // Ensure loading indicator is hidden in all cases
          setTimeout(hideLoading, 500);
        },
      });
    }

    // Reset form validation when modals are closed
    $("#addVariableModal").on("hidden.bs.modal", function () {
      $("#variableName").removeClass("is-invalid");
      $("#addVariableForm")[0].reset();
    });

    $("#editVariableModal").on("hidden.bs.modal", function () {
      $("#editVariableName").removeClass("is-invalid");
      $("#editVariableForm")[0].reset();
    });

    // Initial data fetch
    fetchCustomVariables();
  });
</script>
