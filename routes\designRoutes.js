const express = require("express");
const router = express.Router();
const designController = require("../controllers/designController");
const { checkPermissions } = require("../middleware/middleware");

// Get all designs (with pagination)
router.get(
  "/",
  checkPermissions("Design", ["read"]),
  designController.getDesignsPaginated
);

// Get all designs (with pagination)
router.get(
  "/all",
  checkPermissions("Design", ["read"]),
  designController.getAllDesigns
);

//get all design files associated with the business
router.get(
  "/business/all",
  checkPermissions("Design", ["read"]),
  designController.getBusinessDesignsPaginated
);

// Search designs
router.get(
  "/search",
  checkPermissions("Design", ["read"]),
  designController.searchDesigns
);

// Search designs with pagination
router.get(
  "/search-paginated",
  checkPermissions("Design", ["read"]),
  designController.searchPaginated
);

// Duplicate a design
router.post(
  "/:id/duplicate",
  checkPermissions("Design", ["create"]),
  designController.duplicateDesign
);

// Other existing routes
router.get(
  "/:id",
  checkPermissions("Design", ["read"]),
  designController.getDesignById
);

router.post(
  "/",
  checkPermissions("Design", ["create"]),
  designController.createDesign
);

router.put(
  "/:id",
  checkPermissions("Design", ["update"]),
  designController.updateDesign
);

router.put(
  "/:id/update-isdeleted",
  checkPermissions("Design", ["update"]),
  designController.updateDesignStatus
);

router.put(
  "/:id/update-isdeleted-restore",
  checkPermissions("Design", ["update"]),
  designController.updateDesignStatusRestore

);

router.delete(
  "/:id",
  checkPermissions("Design", ["delete"]),
  designController.deleteDesign
);



router.get(
  "/deleted/get",
  checkPermissions("Design", ["read"]),
  designController.getDeletedDesigns
);

module.exports = router;
