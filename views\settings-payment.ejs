<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title": "Payment Settings" }) %>
<!-- header content goes here -->
<style>
  /* Modern SaaS pricing card styles */
  .pricing-section {
    padding: 20px 0;
  }

  .pricing-card {
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    background-color: #ffffff;
    margin-bottom: 30px;
    position: relative;
    overflow: visible;
  }

  .pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(81, 86, 190, 0.15);
    border-color: #5156be;
  }

  .pricing-header {
    padding: 25px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    position: relative;
    height: auto;
    display: flex;
    flex-direction: column;
  }

  .pricing-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #5156be;
    color: white;
    padding: 3px 10px;
    font-size: 11px;
    font-weight: 600;
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  .plan-name {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: inline-block;
  }

  .current-plan {
    display: inline-block;
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    vertical-align: middle;
  }

  .price-container {
    margin: 15px 0;
  }

  .pricing-card.featured .price-container {
    margin: 15px 0;
    text-align: center;
    position: relative;
    display: inline-block;
  }

  .pricing-card.featured .price-container:before {
    content: "";
    position: absolute;
    width: 30px;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.4);
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
  }

  .price {
    font-size: 36px;
    font-weight: 700;
    color: #5156be;
    line-height: 1;
    display: inline-block;
  }

  .price-period {
    font-size: 14px;
    color: #6c757d;
    display: block;
    margin-top: 5px;
  }

  .plan-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .certificate-limit {
    display: inline-block;
    background-color: rgba(81, 86, 190, 0.1);
    color: #5156be;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 13px;
  }

  .pricing-body {
    padding: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .pricing-cta {
    padding: 0 25px 25px;
  }

  .read-more-btn {
    background-color: #f8f9fa;
    color: #5156be;
    border: 1px solid #5156be;
    border-radius: 6px;
    padding: 10px 15px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 20px;
    font-size: 14px;
    display: block;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    position: relative;
    z-index: 10;
  }

  .read-more-btn:hover {
    background-color: rgba(81, 86, 190, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .pricing-card.featured .read-more-btn {
    color: white;
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
  }

  .pricing-card.featured .read-more-btn:hover {
    background-color: rgba(255, 255, 255, 0.35);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: white;
  }

  /* Modal styling */
  .plan-modal .modal-header {
    background: #5156be;
    color: white;
    border-radius: 6px 6px 0 0;
  }

  .plan-modal .modal-title {
    font-weight: 600;
  }

  .plan-modal .modal-body {
    padding: 25px;
  }

  .plan-modal .modal-footer {
    border-top: 1px solid #eee;
    padding: 15px 25px;
  }

  .plan-modal .btn-primary {
    background-color: #5156be;
    border-color: #5156be;
  }

  .plan-modal .btn-primary:hover {
    background-color: #4347a0;
    border-color: #4347a0;
  }

  .plan-modal .modal-price {
    font-size: 32px;
    font-weight: 700;
    color: #5156be;
    margin-bottom: 15px;
  }

  .plan-modal .modal-price-period {
    font-size: 14px;
    color: #6c757d;
  }

  .plan-modal .feature-category {
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
  }

  .feature-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    font-size: 15px;
  }

  .feature-list {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 15px;
  }

  .feature-list li {
    padding: 5px 0;
    color: #6c757d;
    position: relative;
    padding-left: 25px;
    font-size: 14px;
  }

  .feature-list li:before {
    content: "✓";
    color: #5156be;
    position: absolute;
    left: 0;
    font-weight: bold;
  }

  .feature-list.limited {
    max-height: 180px;
    overflow-y: auto;
    margin-bottom: 10px;
    padding-right: 5px;
  }

  .pricing-card.featured .feature-list.limited {
    max-height: 180px;
  }

  .feature-list.limited::-webkit-scrollbar {
    width: 4px;
  }

  .feature-list.limited::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  .feature-list.limited::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }

  .btn-demo {
    background-color: #5156be;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 15px;
    cursor: pointer;
  }

  .btn-demo:hover {
    background-color: #4347a0;
  }

  .btn-choose {
    background-color: #5156be;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
  }

  .btn-choose:hover {
    background-color: #4347a0;
    color: white;
    text-decoration: none;
  }

  .btn-outline-choose {
    background-color: transparent;
    color: #5156be;
    border: 1px solid #5156be;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
  }

  .btn-outline-choose:hover {
    background-color: #5156be;
    color: white;
    text-decoration: none;
  }

  .money-back {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    margin-bottom: 0;
  }

  /* Featured card styles */
  .pricing-card.featured {
    border: 2px solid #5156be;
    z-index: 2;
    box-shadow: 0 10px 30px rgba(81, 86, 190, 0.2);
    height: 100%;
  }

  .pricing-card.featured:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(81, 86, 190, 0.25);
  }

  .pricing-card.featured .pricing-header {
    background: linear-gradient(135deg, #5156be, #3a3d8a);
    color: white;
    border-bottom: none;
    position: relative;
    overflow: hidden;
  }

  .pricing-card.featured {
    background-color: #4347a0;
    color: white;
  }

  .pricing-card.featured .pricing-header:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1) 0%, transparent 60%);
    z-index: 0;
  }

  .pricing-card.featured .pricing-header > * {
    position: relative;
    z-index: 1;
  }

  .pricing-card.featured .plan-name {
    color: white;
    font-weight: 700;
  }

  .pricing-card.featured .price {
    color: #ffffff;
    font-size: 42px;
    font-weight: 800;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
    background: rgba(255, 255, 255, 0.25);
    padding: 5px 15px;
    border-radius: 8px;
    display: inline-block;
    margin: 5px 0;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .pricing-card.featured .price:before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 9px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.4), transparent);
    z-index: -1;
    opacity: 0.5;
  }

  .pricing-card.featured .price-period {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
  }

  .pricing-card.featured .plan-description {
    color: rgba(255, 255, 255, 0.9);
  }

  .pricing-card.featured .certificate-limit {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .pricing-card.featured .pricing-badge {
    background-color: #5156be;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }

  .pricing-card.featured .feature-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
  }

  .pricing-card.featured .feature-list li {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 500;
  }

  .pricing-card.featured .feature-list li:before {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .pricing-card.featured .money-back {
    color: rgba(255, 255, 255, 0.8);
  }

  .pricing-card.featured .text-white {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Tab styling */
  .nav-tabs-custom .nav-link {
    border: 1px solid #dee2e6;
    background-color: #f8f9fa;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .nav-tabs-custom .nav-link.active {
    background-color: #5156be;
    color: white;
    border-color: #5156be;
  }

  .billing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
  }

  .billing-toggle .toggle-label {
    font-weight: 500;
    margin: 0 15px;
  }

  .billing-toggle .toggle-label.active {
    color: #5156be;
  }

  /* Certificate limit badge */
  .certificate-limit {
    display: inline-block;
    background-color: rgba(81, 86, 190, 0.1);
    color: #5156be;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 500;
    margin-bottom: 20px;
  }

  .pricing-card.featured .certificate-limit {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }

  /* Current plan badge */
  .current-plan {
    display: inline-block;
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    vertical-align: middle;
  }
</style>


<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Payment Settings" , "pagetitle": "Settings" }) %>


<div >
    <div id="screen-2" class="screen hidden">
    <div class="row">
      <div class="col-lg-12">
        <div class="card">
          <div class="card-header d-flex">
            <h4 class="card-title mb-0 flex-grow-1">Choose your plan</h4>
            <div class="flex-shrink-0 align-self-end">
              <ul
                class="nav justify-content-end nav-tabs-custom rounded card-header-tabs"
                id="pills-tab"
                role="tablist"
              >
                <li class="nav-item">
                  <a
                    class="nav-link px-3 rounded monthly active"
                    id="monthly"
                    data-bs-toggle="pill"
                    href="#month"
                    role="tab"
                    aria-controls="month"
                    aria-selected="true"
                    >Monthly</a
                  >
                </li>
                <li class="nav-item">
                  <a
                    class="nav-link px-3 rounded yearly"
                    id="yearly"
                    data-bs-toggle="pill"
                    href="#year"
                    role="tab"
                    aria-controls="year"
                    aria-selected="false"
                    >Yearly</a
                  >
                </li>
              </ul>
            </div>
          </div>
          <div class="card-body">
            <div class="tab-content" id="pills-tabContent">
              <div
                class="tab-pane fade active show"
                id="month"
                role="tabpanel"
                aria-labelledby="monthly"
              >
                <div class="row pricing-section">
                  <!-- Free Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Free <span class="current-plan">Current Plan</span></h5>
                        <div class="price-container">
                          <span class="price">$0</span>
                        </div>
                        <div class="price-period">per month</div>
                        <p class="plan-description">For individuals and small teams getting started.</p>
                        <div class="certificate-limit">100 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>1 workspace & 1 user</li>
                          <li>25 events</li>
                          <li>Basic Certificate Builder</li>
                          <li>Download Certificate</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#freePlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                       
                      </div>
                    </div>
                  </div>

                  <!-- Standard Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Standard</h5>
                        <div class="price-container">
                          <span class="price">$49</span>
                        </div>
                        <div class="price-period">per month</div>
                        <p class="plan-description">For starters and small businesses.</p>
                        <div class="certificate-limit">1,250 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>1 workspace & 3 users</li>
                          <li>125 events</li>
                          <li>Certificate Builder</li>
                          <li>Custom Variables</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#standardPlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Professional Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Professional</h5>
                        <div class="price-container">
                          <span class="price">$299</span>
                        </div>
                        <div class="price-period">per month</div>
                        <p class="plan-description">For businesses and organizations.</p>
                        <div class="certificate-limit">3,000 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>Everything in Standard</li>
                          <li>300 events</li>
                          <li>Certificate Verification Page</li>
                          <li>Custom Domain</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#professionalPlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Enterprise Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Enterprise</h5>
                        <div class="price-container">
                          <span class="price">$999</span>
                          <!-- <div class="pricing-badge">Featured</div> -->
                        </div>
                        <div class="price-period">per month</div>
                        <p class="plan-description">For enterprise, universities & events.</p>
                        <div class="certificate-limit">10,000 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>Everything in Professional</li>
                          <li>1000 events</li>
                          <li>Dedicated Account Manager</li>
                          <li>REST API</li>

                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#enterprisePlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                  <!-- end col -->

                  <!-- end col -->
                </div>
                <!-- end row -->
              </div>
              <!-- end tab pane -->
              <div
                class="tab-pane fade"
                id="year"
                role="tabpanel"
                aria-labelledby="yearly"
              >
                <div class="row pricing-section">
                  <!-- Free Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Free <span class="current-plan">Current Plan</span></h5>
                        <div class="price-container">
                          <span class="price">$0</span>
                        </div>
                        <div class="price-period">per year</div>
                        <p class="plan-description">For individuals and small teams getting started.</p>
                        <div class="certificate-limit">100 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>1 workspace & 1 user</li>
                          <li>25 events</li>
                          <li>Basic Certificate Builder</li>
                          <li>Download Certificate</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#freePlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                        <p class="money-back">30-day money back・Cancel any time</p>
                      </div>
                    </div>
                  </div>

                  <!-- Standard Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Standard</h5>
                        <div class="price-container">
                          <span class="price">$49</span>
                        </div>
                        <div class="price-period">per year</div>
                        <p class="plan-description">For starters and small businesses.</p>
                        <div class="certificate-limit">1,250 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>1 workspace & 3 users</li>
                          <li>125 events</li>
                          <li>Certificate Builder</li>
                          <li>Custom Variables</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#standardPlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                        <p class="money-back">30-day money back・Cancel any time</p>

                      </div>
                    </div>
                  </div>

                  <!-- Professional Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card">
                      <div class="pricing-header">
                        <h5 class="plan-name">Professional</h5>
                        <div class="price-container">
                          <span class="price">$299</span>
                        </div>
                        <div class="price-period">per year</div>
                        <p class="plan-description">For businesses and organizations.</p>
                        <div class="certificate-limit">3,000 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>Everything in Standard</li>
                          <li>300 events</li>
                          <li>Certificate Verification Page</li>
                          <li>Custom Domain</li>
                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#professionalPlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                        <p class="money-back">30-day money back・Cancel any time</p>
                      </div>
                    </div>
                  </div>

                  <!-- Enterprise Plan -->
                  <div class="col-xl-3 col-sm-6">
                    <div class="pricing-card ">
                      <div class="pricing-header">
                        <h5 class="plan-name">Enterprise</h5>
                        <div class="price-container">
                          <span class="price">$999</span>
                          <!-- <div class="pricing-badge">Featured</div> -->
                        </div>
                        <div class="price-period">per year</div>
                        <p class="plan-description">For enterprise, universities & events.</p>
                        <div class="certificate-limit">10,000 certificates</div>
                      </div>

                      <div class="pricing-body">
                        <h6 class="feature-title">Key Features</h6>
                        <ul class="feature-list">
                          <li>Everything in Professional</li>
                          <li>1000 events</li>
                          <li>Dedicated Account Manager</li>
                          <li>REST API</li>

                        </ul>
                        <button class="read-more-btn" data-bs-toggle="modal" data-bs-target="#enterprisePlanModal">
                          <i class="fas fa-info-circle me-2"></i>Read More
                        </button>
                      </div>

                      <div class="pricing-cta">
                        <button class="btn-demo">
                          <i class="fas fa-calendar-check me-2"></i>Schedule a Demo
                        </button>
                        <p class="money-back">30-day money back・Cancel any time</p>
                      </div>
                    </div>
                  </div>
                </div>

                  <!-- end col -->

                  <!-- end col -->
                </div>
              </div>
              <!-- end tab pane -->
            </div>
            <!-- end tab content -->
          </div>
          <!-- end card body -->
        </div>
        <!-- end card -->
      </div>
      <!-- end col -->
    </div>
    <!-- end row -->


  </div>
  </div>




<%- contentFor('FooterJs') %>

<!-- Free Plan Modal -->
<div class="modal fade plan-modal" id="freePlanModal" tabindex="-1" aria-labelledby="freePlanModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="freePlanModalLabel">Free Plan Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="modal-price">$0 <span class="modal-price-period" id="freePlanPeriod">per month</span></div>
        <p>For individuals and small teams getting started.</p>
        <div class="certificate-limit mb-4">100 certificates</div>

        <h6 class="feature-category">Account Details</h6>
        <ul class="feature-list">
          <li>1 workspace & 1 user</li>
          <li>25 events</li>
        </ul>

        <h6 class="feature-category">Key Features</h6>
        <ul class="feature-list">
          <li>Basic Certificate Builder</li>
          <li>Download Certificate</li>
          <li>Badges and Widgets</li>
          <li>QR Code URL</li>
        </ul>

        <h6 class="feature-category">Support</h6>
        <ul class="feature-list">
          <li>Email Support</li>
          <li>Knowledge Base Access</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button id="freePlanButton" data-plan-details="Free,0,100" class="btn btn-primary">Choose Plan</button>
      </div>
    </div>
  </div>
</div>

<!-- Standard Plan Modal -->
<div class="modal fade plan-modal" id="standardPlanModal" tabindex="-1" aria-labelledby="standardPlanModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="standardPlanModalLabel">Standard Plan Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="modal-price">$49 <span class="modal-price-period" id="standardPlanPeriod">per month</span></div>
        <p>For starters and small businesses.</p>
        <div class="certificate-limit mb-4">1,250 certificates</div>

        <h6 class="feature-category">Account Details</h6>
        <ul class="feature-list">
          <li>1 workspace & 3 users</li>
          <li>125 events</li>
        </ul>

        <h6 class="feature-category">Key Features</h6>
        <ul class="feature-list">
          <li>Certificate Builder</li>
          <li>Download Certificate</li>
          <li>Badges and Widgets</li>
          <li>eBadges and eWidgets</li>
          <li>Custom Variables</li>
          <li>QR Code URL</li>
          <li>Bulk Emails to Participants</li>
        </ul>

        <h6 class="feature-category">Support</h6>
        <ul class="feature-list">
          <li>Premium Onboarding</li>
          <li>Phone/Zoom/Email Support</li>
          <li>Account Support</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button id="standardPlanButton" data-plan-details="Standard,49,1250" class="btn btn-primary">Choose Plan</button>
      </div>
    </div>
  </div>
</div>

<!-- Professional Plan Modal -->
<div class="modal fade plan-modal" id="professionalPlanModal" tabindex="-1" aria-labelledby="professionalPlanModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="professionalPlanModalLabel">Professional Plan Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="modal-price">$299 <span class="modal-price-period" id="professionalPlanPeriod">per month</span></div>
        <p>For businesses and organizations.</p>
        <div class="certificate-limit mb-4">3,000 certificates</div>

        <h6 class="feature-category">Account Details</h6>
        <ul class="feature-list">
          <li>1 workspace & 3 users</li>
          <li>300 events</li>
        </ul>

        <h6 class="feature-category">Key Features</h6>
        <ul class="feature-list">
          <li>Everything in Standard</li>
          <li>Create Forms to Collect Data</li>
          <li>Bulk Emails to Participants</li>
          <li>Certificate Verification Page</li>
          <li>Custom Domain</li>
          <li>Custom Emails</li>
          <li>Brand Domain Emails</li>
        </ul>

        <h6 class="feature-category">Support</h6>
        <ul class="feature-list">
          <li>Premium Onboarding</li>
          <li>Phone/Zoom/Email Support</li>
          <li>Dedicated Account Manager</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button id="professionalPlanButton" data-plan-details="Professional,299,3000" class="btn btn-primary">Choose Plan</button>
      </div>
    </div>
  </div>
</div>

<!-- Enterprise Plan Modal -->
<div class="modal fade plan-modal" id="enterprisePlanModal" tabindex="-1" aria-labelledby="enterprisePlanModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="enterprisePlanModalLabel">Enterprise Plan Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="modal-price">$999 <span class="modal-price-period" id="enterprisePlanPeriod">per month</span></div>
        <p>For enterprise, universities & events.</p>
        <div class="certificate-limit mb-4">10,000 certificates</div>

        <h6 class="feature-category">Account Details</h6>
        <ul class="feature-list">
          <li>1 workspace & 3 users</li>
          <li>1000 events</li>
        </ul>

        <h6 class="feature-category">Key Features</h6>
        <ul class="feature-list">
          <li>Everything in Professional</li>
          <li>Extra Events on request</li>
          <li>Premium Onboarding</li>
          <li>Verified issuer status</li>
          <li>On Request SLAs and DPA</li>
          <li>Certificate Request Form</li>
          <li>Promo on Verification Page</li>
          <li>Past Certificates (Watermarked)</li>
          <li>REST API</li>
        </ul>

        <h6 class="feature-category">Add-ons Available</h6>
        <ul class="feature-list">
          <li>Private Server</li>
          <li>Candidate Profile</li>
          <li>Past Certificates</li>
          <li>Custom Verification Portal</li>
          <li>OTP Based Verification</li>
          <li>Request Based Verification</li>
          <li>Facebook Pixel</li>
          <li>Custom Analytics Code</li>
          <li>Visitor Counter</li>
          <li>Certificate Visitor Counter</li>
        </ul>

        <h6 class="feature-category">Support</h6>
        <ul class="feature-list">
          <li>Dedicated Account Manager</li>
          <li>Dedicated Support Manager</li>
          <li>Priority Support</li>
        </ul>

        <p class="mt-3"><strong>Note:</strong> This plan may cost extra based on add-ons and may take implementation time.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button id="enterprisePlanButton" data-plan-details="Enterprise,999,10000," class="btn btn-primary">Choose Plan</button>
      </div>
    </div>
  </div>
</div>



<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>

<script>
  $(document).ready(function(){
   let billingCycle="monthly";
   let paddleToken=undefined;
   let paddlePriceId='';
   let planTier='';
   let certificateLimit=0;
   let planPrice=0;

   function fetchPaddleToken(){
    $.ajax({
      url: "/api/payments/get-paddle-token",
      method: "GET",
      success: function(response) {

       if(response.success) paddleToken = response.token;
       initializePaddle();

      },
      error: function(xhr, status, error) {
        console.error("Error fetching Paddle token:", error);
      }
    });
   }

   function savePayment(event){
    console.log("Paddle event received:", event);

    if(event.name==="checkout.completed"){
      
      console.log("Full event data:", JSON.stringify(event.data, null, 2));
      const paymentMethod = event.data.payment &&
                           event.data.payment.method_details ?
                           event.data.payment.method_details.type : "card";

      const totalAmount = event.data.totals ? event.data.totals.total : 0;

    
      const transactionId = event.data.transaction_id || '';
                           

      console.log("Transaction ID extracted:", transactionId);

      const data = {
        planName: planTier,
        certificateLimit: certificateLimit,
        paddleTransactionId: transactionId,
        paymentStatus: event.data.status,
        paymentMethod: paymentMethod,
        totalAmountPaid: totalAmount,
        billingCycle: billingCycle,
        customer_id: event.data.customer.id
       
      };

      console.log("Saving subscription data:", data);

      $.ajax({
        url: "/api/payments/save-subscription",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify(data),
        success: function(response) {
          console.log("Subscription saved successfully:", response);
          window.location.href = "/subscription";
          
          
          
        },
        error: function(xhr, status, error) {
          console.error("Error saving subscription:", error);
          console.error("Response:", xhr.responseText);
          window.location.href = "/subscription";
         
           
         
        }
      });
    }
   }

   function initializePaddle(){
    if(window.Paddle){
      Paddle.Environment.set("sandbox");
      Paddle.Initialize({
        token: paddleToken,
        checkout:{
        settings:{
          theme:"light",
          showAddDiscounts: false,        
          allowDiscountRemoval: false    
        }
        },
        
        eventCallback:function(event){
          savePayment(event)
        }
      })
    }

   }

   fetchPaddleToken();

   // Function to update modal price periods based on billing cycle
   function updatePricePeriods() {
     if (billingCycle === "monthly") {
       $(".modal-price-period").text("per month");
     } else {
       $(".modal-price-period").text("per year");
     }
   }

   // Update price periods when tabs are clicked
   $("#monthly").click(function(){
     billingCycle="monthly";
     $('body').attr('data-billing-cycle', 'monthly');
     updatePricePeriods();
   });

   $("#yearly").click(function(){
     billingCycle="yearly";
     $('body').attr('data-billing-cycle', 'yearly');
     updatePricePeriods();
   });

   // Update price periods when modals are opened
   $('.plan-modal').on('show.bs.modal', function () {
     // Double-check the current billing cycle from the data attribute
     billingCycle = $('body').attr('data-billing-cycle') || 'monthly';
     updatePricePeriods();
   });

   // Initialize price periods and data attribute
   $('body').attr('data-billing-cycle', 'monthly');
   updatePricePeriods();



   $(document).on("click","#freePlanButton",function(){
    const planDetails = $(this).data("plan-details");
    const [planName, price, totalCertificateLimit] = planDetails.split(",");
    planTier=planName;
    certificateLimit=totalCertificateLimit;
    planPrice=price;
    fetchPriceIdOfChoosenPlan(billingCycle,planName);

   });


   $(document).on("click","#standardPlanButton",function(){
    const planDetails = $(this).data("plan-details");
    const [planName, price,  totalCertificateLimit] = planDetails.split(",");
     planTier=planName;
    certificateLimit=totalCertificateLimit;
     planPrice=price;
     fetchPriceIdOfChoosenPlan(billingCycle,planName);
});


   $(document).on("click","#professionalPlanButton",function(){
    const planDetails = $(this).data("plan-details");
    const [planName, price,  totalCertificateLimit] = planDetails.split(",");
    planTier=planName;
    certificateLimit=totalCertificateLimit;
     planPrice=price;
      fetchPriceIdOfChoosenPlan(billingCycle,planName);
   });


   $(document).on("click","#enterprisePlanButton",function(){
    const planDetails = $(this).data("plan-details");
    const [planName, price,  totalCertificateLimit] = planDetails.split(",");
     planTier=planName;
     certificateLimit=totalCertificateLimit;
    planPrice=price;
    fetchPriceIdOfChoosenPlan(billingCycle,planName);
   });


   function fetchPriceIdOfChoosenPlan(billingCycle,planTier){

    $.ajax({
      url: `/api/payments/get-price-id?billingCycle=${billingCycle}&planTier=${planTier}`,
      method: "GET",

      success: function(response) {
        console.log(response,'response');
        if(response.success){
          paddlePriceId = response.priceId;
          openPaddleCheckout();
        }
      },
      error: function(xhr, status, error) {
        console.error("Error fetching price id:", error);
      }
    });

   }

   function openPaddleCheckout(){
     if(window.Paddle){
      Paddle.Checkout.open({
     items:[
      {
        priceId: paddlePriceId,
        quantity: 1
      }
     ],
     discountCode:"3QXKPGFB99"
     })
     }
   }

    // Fetch all subscriptions (both active and cancelled)
    function fetchSubscriptionDetails() {
      $.ajax({
        url: "/api/payments/active-subscription",
        method: "GET",
        success: function(response) {
          if(response.success) {
           console.log(response,'response from db  subscriptions')
          };
          
        },
        error: function(xhr, status, error) {
          console.error("Error fetching subscription details:", error);
        
        }
      });
    
    
    }

    fetchSubscriptionDetails();
  })
</script>