const mongoose = require("mongoose");

const discountSchema = new mongoose.Schema({
  description: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: ["percentage", "fixed"],
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  enabled_for_checkout: {
    type: Boolean,
    default: true,
  },
  recur: {
    type: Boolean,
    default: false,
  },
  maximum_recurring_intervals: {
    type: Number,
    default: null,
    validate: {
      validator: function (value) {
        // If recur is true, this field must be a non-negative number
        if (this.recur) {
          return value !== null && value >= 0;
        }
        // If recur is false, allow null
        return value === null;
      },
      message:
        "If recur is true, maximum_recurring_intervals must be a number >= 0",
    },
  },
  expires_at: {
    type: Date,
    required: true,
  },
  created_at: {
    type: Date,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    default: Date.now,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  businessId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Business",
  },
  discountId: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: ["active", "inactive"],
    default: "active",
  },
  code: {
    type: String,
    required: true,
  },
  mode: {
    type: String,
  },
  currency_code: {
    type: String,
    default: "USD",
  },
  times_used: {
    type: Number,
    default: 0,
  },
  shouldBeApplied: {
    type: Boolean,
    default: false,
  },
});

module.exports = mongoose.model("Discount", discountSchema);
