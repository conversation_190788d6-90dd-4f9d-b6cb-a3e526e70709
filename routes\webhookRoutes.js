const express = require("express");
const router = express.Router();
const { Environment, EventName, Paddle } = require("@paddle/paddle-node-sdk");
const Payment = require("../models/Payment");
const Plan = require("../models/Plan");
const sendEmail = require("../utils/email");
const User = require("../models/User");

const paddle = new Paddle(process.env.PADDLE_API_KEY, {
  environment: Environment.sandbox,
});

router.post(
  "/",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const signature = req.headers["paddle-signature"] || "";

    const rawRequestBody = req.body.toString() || "";

    const secretKey = process.env.WEBHOOK_SECRET_KEY || "";

    try {
      if (signature && rawRequestBody) {
        // The `unmarshal` function will validate the integrity of the webhook and return an entity
        const eventData = await paddle.webhooks.unmarshal(
          rawRequestBody,
          secretKey,
          signature
        );
        switch (eventData.eventType) {
          case EventName.SubscriptionCanceled:
            console.log(`Subscription ${eventData.data.id} was cancelled`);
            cancelSubscription(req, res, eventData);
            break;
          case EventName.PaymentMethodSaved:
            savePaymentMethod(req, res, eventData);
            break;
          default:
            console.log(eventData.eventType);
        }
      } else {
        console.log("Signature missing in header");
      }
    } catch (e) {
      console.log("Error in webhook:", e);
      return res.status(400).json({ message: "Bad request" });
    }
  }
);

async function cancelSubscription(req, res, eventData) {
  try {
    const subscriptionId = eventData?.data?.id.toString() || "";
    const customerId = eventData?.data?.customerId.toString() || "";

    if (!subscriptionId) {
      console.error("Subscription ID missing from webhook data");
      return res.status(400).json({ message: "Subscription ID missing" });
    }

    if (!customerId) {
      console.error("Customer ID missing from webhook data");
      return res.status(400).json({ message: "Customer ID missing" });
    }

    // Find the subscription in our database using subscription ID and customer ID
    const subscription = await Payment.findOne({
      paddleSubscriptionId: subscriptionId,
      customer_id: customerId,
    });

    if (!subscription) {
      console.error("Subscription not found in database:", subscriptionId);
      return res.status(404).json({ message: "Subscription not found" });
    }

    console.log("Found subscription in database:", subscription._id);
    await Plan.findByIdAndUpdate(subscription.planId, { status: "inactive" });

    // Update the subscription status
    const updatedSubscription = await Payment.findOneAndUpdate(
      {
        paddleSubscriptionId: subscriptionId,
        customer_id: customerId,
        userId: subscription.userId,
      },
      {
        $set: {
          isSubscriptionCancelled: true,
          cancelledAt: new Date(),
          isAutoRenew: false,
          updatedAt: new Date(),
        },
      },
      {
        new: true,
      }
    );

    console.log("Updated subscription:", updatedSubscription._id);
    const user = await User.findById(subscription.userId);

    if (user) {
      // Send confirmation email
      const message = `Dear ${
        user.firstName || "User"
      },\n\nYour subscription has been successfully cancelled effective immediately. If you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.\n\nBest regards,\nThe MixCertificate Team`;

      await sendEmail({
        email: user.email,
        subject: "Subscription Cancellation Confirmation",
        message,
      });

      console.log("Cancellation email sent successfully");
    }

    return res.redirect("/subscription");
  } catch (err) {
    console.error("Error processing subscription cancellation:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
}

async function savePaymentMethod(req, res, eventData) {
  try {
    console.log("Payment method saved:", eventData.data);

    // eventData.data payload  below
    //     {
    //   id: 'paymtd_01hs8zx6x377xfsfrt2bqsevbw',
    //   customerId: 'ctm_01hv6y1jedq4p1n0yqn5ba3ky4',
    //   addressId: 'add_01hv8gq3318ktkfengj2r75gfx',
    //   type: 'card',
    //   origin: 'subscription',
    //   savedAt: '2024-05-02T02:55:25.198953Z',
    //   updatedAt: '2024-05-02T02:55:25.198953Z'
    // }
  } catch (err) {
    console.error("Error processing payment method save:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
}

module.exports = router;
