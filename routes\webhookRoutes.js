const express = require("express");
const router = express.Router();
const { Environment, EventName, Paddle } = require("@paddle/paddle-node-sdk");
const Payment = require("../models/Payment");

const sendEmail = require("../utils/email");

const paddle = new Paddle(process.env.PADDLE_API_KEY, {
  environment: Environment.sandbox,
});

router.post(
  "/",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const signature = req.headers["paddle-signature"] || "";
    console.log("Signature:", signature);
    console.log("Request Body:", req.body);
    console.log(process.env.WEBHOOK_SECRET_KEY);
    console.log("api key",process.env.PADDLE_API_KEY);
    // req.body should be of type `buffer`, convert to string before passing it to `unmarshal`.
    // If express returned a JSON, remove any other middleware that might have processed raw request to object
    const rawRequestBody = req.body.toString() || "";
    // Replace `WEBHOOK_SECRET_KEY` with the secret key in notifications from vendor dashboard
    const secretKey = process.env.WEBHOOK_SECRET_KEY || "";

    try {
      if (signature && rawRequestBody) {
        // The `unmarshal` function will validate the integrity of the webhook and return an entity
        const eventData = await paddle.webhooks.unmarshal(
          rawRequestBody,
          secretKey,
          signature
        );
        switch (eventData.eventType) {
          case EventName.SubscriptionCanceled:
            console.log(`Subscription ${eventData.data.id} was cancelled`);
            await cancelSubscription(req, res, eventData.data);
            break;

          default:
            console.log(eventData.eventType);
        }
      } else {
        console.log("Signature missing in header");
      }
    } catch (e) {
      // Handle signature mismatch or other runtime errors
      console.log(e);
    }
    // Return a response to acknowledge
    res.send("Processed webhook event");
  }
);

module.exports = router;

async function cancelSubscription(req, res, eventData) {
  try {
    console.log(req.body, "req.body");
    console.log(eventData, "eventData");
    console.log(eventData.data, "eventData.data");
    const subscriptionId = eventData?.data?.id || "";
    const customerId = eventData?.data?.customer_id || "";
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        error: "User not authenticated",
        success: false,
      });
    }

    if (!subscriptionId) {
      return res.status(400).json({
        error: "Subscription ID is required",
        success: false,
      });
    }
    if (!customerId) {
      return res.status(400).json({
        error: "Customer ID is required",
        success: false,
      });
    }

    // // Use the Paddle API key for authentication
    // const apiKey = process.env.PADDLE_API_KEY;
    // const paddleApiUrl = "https://sandbox-api.paddle.com";

    // const response = await fetch(
    //   `${paddleApiUrl}/subscriptions/${subscriptionId}/cancel`,
    //   {
    //     method: "POST",
    //     headers: {
    //       "Content-Type": "application/json",
    //       Authorization: `Bearer ${apiKey}`,
    //     },
    //     body: JSON.stringify({ effective_from: "immediately" }),
    //   }
    // );

    // const data = await response.json();

    // if (!response.ok) {
    //   return res.status(response.status).json({
    //     error: data.error?.message || "Failed to cancel subscription",
    //     success: false,
    //     details: data,
    //   });
    // }

    // Delete payment record immediately
    await Payment.findOneAndUpdate(
      {
        paddleSubscriptionId: subscriptionId,
        userId: userId,
        customer_id: customerId,
      },
      {
        $set: {
          isSubscriptionCancelled: true,
          cancelledAt: new Date(),
          isAutoRenew: false,
          updatedAt: new Date(),
        },
      },
      {
        new: true,
      }
    );

    // Send confirmation email
    const message = `Dear ${
      req.user.firstName || "User"
    },\n\nYour subscription has been successfully cancelled effective immediately. If you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.\n\nBest regards,\nThe MixCertificate Team`;

    await sendEmail({
      email: req.user.email,
      subject: "Subscription Cancellation Confirmation",
      message,
    });

    res.status(200).json({
      message: "Subscription cancelled successfully",
      success: true,
      // data: data.data,
    });
  } catch (err) {
    console.error("Error cancelling subscription:", err);
    res.status(500).json({
      error: err.message,
      success: false,
    });
  }
}
