const express = require("express");
const router = express.Router();
const { Environment, EventName, Paddle } = require("@paddle/paddle-node-sdk");
const Payment = require("../models/Payment");

const sendEmail = require("../utils/email");
const User = require("../models/User");

const paddle = new Paddle(process.env.PADDLE_API_KEY, {
  environment: Environment.sandbox,
});

// Manual signature verification function

router.post(
  "/",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const signature = req.headers["paddle-signature"] || "";
    console.log("=== WEBHOOK RECEIVED ===");
    console.log("Signature:", signature);
    console.log("Request Body Type:", typeof req.body);
    console.log("Request Body Length:", req.body ? req.body.length : 0);
    console.log(
      "Webhook Secret Key:",
      process.env.WEBHOOK_SECRET_KEY ? "SET" : "NOT SET"
    );
    console.log("API Key:", process.env.PADDLE_API_KEY ? "SET" : "NOT SET");

    // req.body should be of type `buffer`, convert to string before passing it to `unmarshal`.
    // If express returned a JSON, remove any other middleware that might have processed raw request to object
    const rawRequestBody = req.body.toString() || "";
    // Replace `WEBHOOK_SECRET_KEY` with the secret key in notifications from vendor dashboard
    const secretKey = process.env.WEBHOOK_SECRET_KEY || "";

    try {
      if (signature && rawRequestBody) {
        // The `unmarshal` function will validate the integrity of the webhook and return an entity
        const eventData = await paddle.webhooks.unmarshal(
          rawRequestBody,
          secretKey,
          signature
        );
        switch (eventData.eventType) {
          case EventName.SubscriptionCanceled:
            console.log(`Subscription ${eventData.data.id} was cancelled`);
            await cancelSubscription(req, res, eventData.data);
            break;
          case EventName.SubscriptionUpdated:
            console.log(`Subscription ${eventData.data.id} was updated`);
            break;
          default:
            console.log(eventData.eventType);
        }
      } else {
        console.log("Signature missing in header");
      }
    } catch (e) {
      // Handle signature mismatch or other runtime errors
      console.log(e);
    }
    // Return a response to acknowledge
    res.send("Processed webhook event");
  }
);

async function cancelSubscription(req, res, eventData) {
  try {
    console.log("=== PROCESSING SUBSCRIPTION CANCELLATION ===");
    console.log("Event Data:", JSON.stringify(eventData, null, 2));

    const subscriptionId = eventData?.id || "";
    const customerId = eventData?.customer_id || "";

    if (!subscriptionId) {
      console.error("Subscription ID missing from webhook data");
      return  res.status(400).json({ message: "Subscription ID missing" });
    }

    if (!customerId) {
      console.error("Customer ID missing from webhook data");
      return res.status(400).json({ message: "Customer ID missing" });
    }

    console.log("Looking for subscription:", subscriptionId);
    console.log("Customer ID:", customerId);

    // Find the subscription in our database using subscription ID and customer ID
    const subscription = await Payment.findOne({
      paddleSubscriptionId: subscriptionId,
      customer_id: customerId,
    });

    if (!subscription) {
      console.error("Subscription not found in database:", subscriptionId);
      return res.status(404).json({ message: "Subscription not found" });
    }

    console.log("Found subscription in database:", subscription._id);

    // Update the subscription status
    const updatedSubscription = await Payment.findOneAndUpdate(
      {
        paddleSubscriptionId: subscriptionId,
        customer_id: customerId,
        userId: subscription.userId,
      },
      {
        $set: {
          isSubscriptionCancelled: true,
          cancelledAt: new Date(),
          isAutoRenew: false,
          updatedAt: new Date(),
          paymentStatus: "cancelled",
        },
      },
      {
        new: true,
      }
    );

    console.log("Updated subscription:", updatedSubscription._id);
    const user = await User.findById(subscription.userId);

    if (user) {
      console.log("Sending cancellation email to:", user.email);

      // Send confirmation email
      const message = `Dear ${
        user.firstName || "User"
      },\n\nYour subscription has been successfully cancelled effective immediately. If you have any questions or need assistance, please don't hesitate to contact <NAME_EMAIL>.\n\nBest regards,\nThe MixCertificate Team`;

      await sendEmail({
        email: user.email,
        subject: "Subscription Cancellation Confirmation",
        message,
      });

      console.log("Cancellation email sent successfully");
    } else {
      console.error("User not found for subscription:", subscription.userId);
    }

    console.log("Subscription cancellation processed successfully");
  } catch (err) {
    console.error("Error processing subscription cancellation:", err);
  }
}
