<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title": "Subscription Management" }) %>
<style>
  .subscription-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background-color: #ffffff;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
  }

  .subscription-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(81, 86, 190, 0.18);
  }

  .subscription-header {
    padding: 30px;
    background: linear-gradient(135deg, #5156be, #4347a0);
    text-align: center;
    position: relative;
    color: white;
  }

  .subscription-body {
    padding: 30px;
    background-color: #ffffff;
  }

  .subscription-footer {
    padding: 25px 30px;
    background-color: #f8f9fa;
    text-align: center;
    border-top: 1px solid #f0f0f0;
  }

  .plan-name {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
  }

  .plan-price {
    font-size: 36px;
    font-weight: 700;
    color: white;
    line-height: 1;
    display: inline-block;
    margin: 15px 0;
  }

  .plan-period {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    margin-top: 5px;
  }

  .certificate-limit {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 18px;
    border-radius: 30px;
    font-weight: 500;
    font-size: 14px;
    margin: 15px 0 5px;
    backdrop-filter: blur(5px);
  }

  .subscription-status {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 6px 16px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .status-active {
    background-color: rgba(40, 167, 69, 0.9);
    color: white;
  }

  .status-cancelled {
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
  }

  .status-pending {
    background-color: rgba(255, 193, 7, 0.9);
    color: white;
  }

  .btn-cancel {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
  }

  .btn-cancel:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
  }

  .btn-manage {
    background-color: #5156be;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(81, 86, 190, 0.3);
  }

  .btn-manage:hover {
    background-color: #4347a0;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(81, 86, 190, 0.4);
  }

  .subscription-details {
    margin: 10px 0;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-label {
    font-weight: 500;
    color: #6c757d;
  }

  .detail-value {
    font-weight: 600;
    color: #333;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
  }

  /* Group styling */
  .details-group {
    margin-bottom: 20px;
    padding-bottom: 10px;
  }

  .details-group-title {
    font-size: 14px;
    font-weight: 700;
    color: #5156be;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #5156be;
  }

  /* Tabs styling */
  .subscription-tabs .nav-tabs {
    border-bottom: 1px solid rgba(81, 86, 190, 0.2);
  }

  .subscription-tabs .nav-link {
    color: #6c757d;
    font-weight: 600;
    padding: 12px 20px;
    border: none;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    transition: all 0.3s ease;
  }

  .subscription-tabs .nav-link:hover {
    color: #5156be;
    border-bottom-color: rgba(81, 86, 190, 0.3);
  }

  .subscription-tabs .nav-link.active {
    color: #5156be;
    background-color: transparent;
    border-bottom-color: #5156be;
  }

  .subscription-tabs .badge {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 20px;
  }

  /* Pagination styling */
  .pagination-container {
    margin-top: 30px;
  }

  .pagination .page-link {
    color: #5156be;
    border-color: #e0e0e0;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .pagination .page-link:hover {
    background-color: rgba(81, 86, 190, 0.1);
    border-color: rgba(81, 86, 190, 0.2);
  }

  .pagination .page-item.active .page-link {
    background-color: #5156be;
    border-color: #5156be;
    color: white;
  }

  .pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #f8f9fa;
  }

  .no-subscription {
    text-align: center;
    padding: 50px 0;
  }

  .no-subscription h3 {
    margin-bottom: 20px;
    color: #5156be;
  }

  .btn-subscribe {
    background-color: #5156be;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
    cursor: pointer;
  }

  .btn-subscribe:hover {
    background-color: #4347a0;
  }
</style>

<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Subscription Management" , "pagetitle": "Settings" }) %>

<div class="container">
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Your Subscription</h4>



          <!-- Subscription details will be loaded here -->
          <div id="subscriptionContainer">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2">Loading subscription details...</p>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- Cancel Subscription Modal -->
<div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="border-radius: 16px; border: none; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">
      <div class="modal-header" style="background: linear-gradient(135deg, #5156be, #4347a0); color: white; border-bottom: none; border-radius: 16px 16px 0 0; padding: 20px 25px;">
        <h5 class="modal-title" id="cancelSubscriptionModalLabel" style="font-weight: 700; letter-spacing: 0.5px;">
          <i class="fas fa-exclamation-triangle me-2"></i> Cancel Subscription
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" style="padding: 25px;">
        <div class="alert alert-warning" style="border-radius: 8px; background-color: rgba(255, 193, 7, 0.1); border-color: rgba(255, 193, 7, 0.2);">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Important:</strong> This action cannot be undone. If you wish to use our services again, you'll need to purchase a new subscription.
        </div>

        <p class="mb-4">Please select when you would like this cancellation to take effect:</p>

        <div class="mb-4">
          <div class="form-check mb-3" style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; transition: all 0.2s ease;">
            <input class="form-check-input" type="radio" name="cancelTiming" id="cancelImmediately" value="immediately" checked>
            <label class="form-check-label" for="cancelImmediately" style="font-weight: 500; cursor: pointer; width: 100%;">
              <div style="font-weight: 600; color: #dc3545;">Cancel Immediately</div>
              <div class="text-muted" style="font-size: 0.9rem;">You'll lose access to premium features right away</div>
            </label>
          </div>
          <div class="form-check" style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; transition: all 0.2s ease;">
            <input class="form-check-input" type="radio" name="cancelTiming" id="cancelEndOfBilling" value="next_billing_period">
            <label class="form-check-label" for="cancelEndOfBilling" style="font-weight: 500; cursor: pointer; width: 100%;">
              <div style="font-weight: 600; color: #28a745;">Cancel at End of Billing Period</div>
              <div class="text-muted" style="font-size: 0.9rem;">You'll keep access until your current period ends</div>
              <div class="text-warning mt-2" style="font-size: 0.85rem; font-style: italic;">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Note: You won't be able to purchase a new subscription until your current billing period ends.
              </div>
            </label>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="border-top: 1px solid #f0f0f0; padding: 20px 25px; border-radius: 0 0 16px 16px;">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal" style="border-radius: 8px; padding: 10px 20px; font-weight: 500;">
          <i class="fas fa-times me-2"></i>No, Keep My Subscription
        </button>
        <button type="button" class="btn btn-danger" id="confirmCancelSubscription" style="border-radius: 8px; padding: 10px 20px; font-weight: 600; box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);">
          <span class="spinner-border spinner-border-sm d-none" id="cancelSpinner" role="status" aria-hidden="true"></span>
          <i class="fas fa-check-circle me-2"></i>Yes, Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>

<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
<script>
  $(document).ready(function(){
    let currentSubscriptionId = null;
    let paddleToken = undefined;

    // Initialize Paddle
    function fetchPaddleToken(){
      $.ajax({
        url: "/api/payments/get-paddle-token",
        method: "GET",
        success: function(response) {
          if(response.success) {
            paddleToken = response.token;
            initializePaddle();
          }
        },
        error: function(xhr, status, error) {
          console.error("Error fetching Paddle token:", error);
        }
      });
    }

    function initializePaddle(){
      if(window.Paddle){
        Paddle.Environment.set("sandbox");
        Paddle.Initialize({
          token: paddleToken
        });
      }
    }

    // Fetch all subscriptions (both active and cancelled)
    function fetchSubscriptionDetails() {
      $.ajax({
        url: "/api/payments/all-subscriptions",
        method: "GET",
        success: function(response) {
          renderSubscriptionTabs(response);
        },
        error: function(xhr, status, error) {
          console.error("Error fetching subscription details:", error);
          showToast("Failed to load subscription details. Please try again later.");
        }
      });
    }

    // Render subscription tabs with pagination
    function renderSubscriptionTabs(response) {
      const container = $("#subscriptionContainer");
      container.empty();

      if (!response.success) {
        showToast("Failed to load subscription details. Please try again later.");
        return;
      }

      // Check if we have any subscriptions
      const subscriptions = response.subscriptions || [];
      if (subscriptions.length === 0) {
        renderNoSubscriptionView(container);
        return;
      }

      // Separate active and cancelled subscriptions
      const activeSubscriptions = subscriptions.filter(sub => !sub.isSubscriptionCancelled);
      const cancelledSubscriptions = subscriptions.filter(sub => sub.isSubscriptionCancelled);

      // Create tabs container
      const tabsHtml = `
        <div class="subscription-tabs mb-4">
          <ul class="nav nav-tabs" id="subscriptionTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active-subscriptions"
                type="button" role="tab" aria-controls="active-subscriptions" aria-selected="true">
                <i class="fas fa-check-circle me-2"></i>Active Subscriptions
                <span class="badge bg-primary ms-2">${activeSubscriptions.length}</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled-subscriptions"
                type="button" role="tab" aria-controls="cancelled-subscriptions" aria-selected="false">
                <i class="fas fa-times-circle me-2"></i>Cancelled Subscriptions
                <span class="badge bg-secondary ms-2">${cancelledSubscriptions.length}</span>
              </button>
            </li>
          </ul>
          <div class="tab-content mt-4" id="subscriptionTabsContent">
            <div class="tab-pane fade show active" id="active-subscriptions" role="tabpanel" aria-labelledby="active-tab">
              <div id="activeSubscriptionsContainer"></div>
              <div class="pagination-container mt-4" id="activeSubscriptionsPagination"></div>
            </div>
            <div class="tab-pane fade" id="cancelled-subscriptions" role="tabpanel" aria-labelledby="cancelled-tab">
              <div id="cancelledSubscriptionsContainer"></div>
              <div class="pagination-container mt-4" id="cancelledSubscriptionsPagination"></div>
            </div>
          </div>
        </div>
      `;

      container.html(tabsHtml);

      // Render subscriptions with pagination
      renderPaginatedSubscriptions(activeSubscriptions, "#activeSubscriptionsContainer", "#activeSubscriptionsPagination", true);
      renderPaginatedSubscriptions(cancelledSubscriptions, "#cancelledSubscriptionsContainer", "#cancelledSubscriptionsPagination", false);
    }

    // Render paginated subscriptions
    function renderPaginatedSubscriptions(subscriptions, containerSelector, paginationSelector, isActive) {
      const container = $(containerSelector);
      const paginationContainer = $(paginationSelector);

      // Pagination settings
      const itemsPerPage = 1;
      const totalPages = Math.ceil(subscriptions.length / itemsPerPage);
      let currentPage = 1;

      // Function to render current page
      function renderPage(page) {
        container.empty();

        if (subscriptions.length === 0) {
          container.html(`
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              No ${isActive ? 'active' : 'cancelled'} subscriptions found.
            </div>
          `);
          paginationContainer.empty();
          return;
        }

        // Calculate start and end indices for current page
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, subscriptions.length);

        // Get current page items
        const currentItems = subscriptions.slice(startIndex, endIndex);

        // Render each subscription
        currentItems.forEach(subscription => {
          renderSubscriptionCard(container, subscription, isActive);
        });

        // Update pagination
        renderPagination(totalPages, page);
      }

      // Function to render pagination controls
      function renderPagination(totalPages, currentPage) {
        if (totalPages <= 1) {
          paginationContainer.empty();
          return;
        }

        let paginationHtml = `
          <nav aria-label="Subscription pagination">
            <ul class="pagination justify-content-center">
              <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
        `;

        for (let i = 1; i <= totalPages; i++) {
          paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
              <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
          `;
        }

        paginationHtml += `
              <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
            </ul>
          </nav>
        `;

        paginationContainer.html(paginationHtml);

        // Add event listeners to pagination links
        paginationContainer.find('.page-link').on('click', function(e) {
          e.preventDefault();
          const newPage = parseInt($(this).data('page'));
          if (newPage >= 1 && newPage <= totalPages) {
            currentPage = newPage;
            renderPage(currentPage);
          }
        });
      }

      // Initial render
      renderPage(currentPage);
    }

    // Render a single subscription card
    function renderSubscriptionCard(container, subscription, isActive) {
      // Store subscription ID for later use if it's active
      if (isActive) {
        currentSubscriptionId = subscription.paddleSubscriptionId;
      }

      // Format dates
      const startDate = new Date(subscription.startDate);
      const nextBillingDate = subscription.nextBillingDate ? new Date(subscription.nextBillingDate) : null;

      // Create subscription card with modern SaaS design
      const subscriptionHtml = `
          <div class="subscription-card">
            <div class="subscription-header">
              <div class="subscription-status ${getStatusClass(subscription.paymentStatus)}">
                ${capitalizeFirstLetter(subscription.paymentStatus)}
              </div>
              <h5 class="plan-name">${subscription.planName} Plan</h5>
              <div class="certificate-limit">
                <i class="fas fa-certificate me-1"></i> ${subscription.certificateLimit || 'Unlimited'} Certificates
              </div>
            </div>
            <div class="subscription-body">
              <div class="subscription-details">
                <!-- Plan Details Group -->
                <div class="details-group">
                  <h6 class="details-group-title">Plan Details</h6>
                  <div class="detail-item">
                    <span class="detail-label">Plan Type</span>
                    <span class="detail-value">${subscription.planName}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Certificate Limit</span>
                    <span class="detail-value">${subscription.certificateLimit || 'Unlimited'}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Billing Cycle</span>
                    <span class="detail-value">${capitalizeFirstLetter(subscription.billingCycle)}</span>
                  </div>
                </div>

                <!-- Billing Details Group -->
                <div class="details-group">
                  <h6 class="details-group-title">Billing Information</h6>
                  ${subscription.totalAmountPaid ? `
                  <div class="detail-item">
                    <span class="detail-label">Amount Paid</span>
                    <span class="detail-value">$${subscription.totalAmountPaid}</span>
                  </div>
                  ` : ''}
                  ${subscription.paymentMethod ? `
                  <div class="detail-item">
                    <span class="detail-label">Payment Method</span>
                    <span class="detail-value">${capitalizeFirstLetter(subscription.paymentMethod)}</span>
                  </div>
                  ` : ''}
                  <div class="detail-item">
                    <span class="detail-label">Start Date</span>
                    <span class="detail-value">${formatDate(startDate)}</span>
                  </div>
                  ${nextBillingDate ? `
                  <div class="detail-item">
                    <span class="detail-label">Next Billing Date</span>
                    <span class="detail-value">${formatDate(nextBillingDate)}</span>
                  </div>
                  ` : ''}
                </div>

                <!-- Subscription Status Group -->
                <div class="details-group">
                  <h6 class="details-group-title">Subscription Status</h6>
                  <div class="detail-item">
                    <span class="detail-label">Status</span>
                    <span class="detail-value">${capitalizeFirstLetter(subscription.paymentStatus || 'active')}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Auto Renew</span>
                    <span class="detail-value">${subscription.isAutoRenew ? 'Yes' : 'No'}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Subscription Cancelled</span>
                    <span class="detail-value">${subscription.isSubscriptionCancelled ? 'Yes' : 'No'}</span>
                  </div>
                  ${subscription.cancelledAt ? `
                  <div class="detail-item">
                    <span class="detail-label">Cancelled On</span>
                    <span class="detail-value">${formatDate(new Date(subscription.cancelledAt))}</span>
                  </div>
                  ` : ''}
                </div>

                <!-- Technical Details Group -->
                <div class="details-group">
                  <h6 class="details-group-title">Technical Details</h6>
                  ${subscription.paddleTransactionId ? `
                  <div class="detail-item">
                    <span class="detail-label">Transaction ID</span>
                    <span class="detail-value">${subscription.paddleTransactionId || 'Not available'}</span>
                  </div>
                  ` : ''}
                  <div class="detail-item">
                    <span class="detail-label">Subscription ID</span>
                    <span class="detail-value">${subscription.paddleSubscriptionId || 'Not available'}</span>
                  </div>
                  <div class="detail-item">
                     <span class="detail-label">Customer ID</span>
                     <span class="detail-value">${subscription.customer_id || 'Not available'}</span>
                   </div>
                </div>
                </div>
              </div>
            </div>
            <div class="subscription-footer">
              ${!subscription.isSubscriptionCancelled ? `
                <div class="row g-2">
                 
                  <div class="col-12">
                    <button class="btn-manage manage-subscription-btn w-100" data-customer-id="${subscription.customer_id}" data-subscription-id="${subscription.paddleSubscriptionId}">
                      <i class="fas fa-cog me-2"></i>Manage Subscription
                    </button>
                  </div>
                  
                  <div class="col-12">
                    <button class="btn-cancel cancel-subscription-btn w-100" data-subscription-id="${subscription.paddleSubscriptionId || subscription.id}">
                      <i class="fas fa-times-circle me-2"></i>Cancel Subscription
                    </button>
                  </div>
                </div>
              ` : `
                <div class="text-muted mb-3">
                  <i class="fas fa-info-circle me-2"></i>
                  This subscription has been cancelled.
                </div>
                <!-- View Details button commented out
                ${subscription.paddleSubscriptionId ? `
                  <button class="btn-manage manage-subscription-btn w-100" data-subscription-id="${subscription.paddleSubscriptionId}">
                    <i class="fas fa-external-link-alt me-2"></i>View Details in Paddle
                  </button>
                ` : ''}
                -->
              `}
            </div>
          </div>
        `;

        // Append the subscription card to the container
        container.append(subscriptionHtml);

        // Add event listeners to buttons
        // Manage subscription button -
        container.find('.manage-subscription-btn').last().on('click', function() {
          const customerId = $(this).data('customer-id');
          const subscriptionId = $(this).data('subscription-id');
          
          openManageSubscriptionPortal(customerId, subscriptionId);
          
        });
        

        // Cancel subscription button (only for active subscriptions)
        if (!subscription.isSubscriptionCancelled) {
          container.find('.cancel-subscription-btn').last().on('click', function() {
            currentSubscriptionId = $(this).data('subscription-id');
            $("#cancelSubscriptionModal").modal("show");
          });
        }
    }

    // Render the "No Subscription" view
    function renderNoSubscriptionView(container) {
      const noSubscriptionHtml = `
        <div class="subscription-card">
          <div class="subscription-header" style="text-align: center;">
            <i class="fas fa-exclamation-circle" style="font-size: 48px; margin-bottom: 15px;"></i>
            <h5 class="plan-name">No Active Subscription</h5>
            <p style="color: rgba(255, 255, 255, 0.8); margin-top: 10px;">You don't have an active subscription at the moment.</p>
          </div>
          <div class="subscription-body" style="text-align: center; padding: 40px 30px;">
            <p class="mb-4">Choose a plan to get started with our premium features and create certificates for your events.</p>
            <a href="/settings-plans/" class="btn btn-primary btn-lg" style="background-color: #5156be; border-color: #5156be; padding: 12px 30px; border-radius: 8px; box-shadow: 0 4px 10px rgba(81, 86, 190, 0.3);">
              <i class="fas fa-shopping-cart me-2"></i>View Plans
            </a>
          </div>
        </div>
      `;

      container.html(noSubscriptionHtml);
    }

    // Helper functions
    function getStatusClass(status) {
      switch(status) {
        case 'completed':
        case 'active':
        case 'paid':
          return 'status-active';
        case 'cancelled':
        case 'canceled':
          return 'status-cancelled';
        default:
          return 'status-pending';
      }
    }

    function capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    }

    function formatDate(date) {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }

   

    // Show toast notification
    



    // Cancel subscription
    $("#confirmCancelSubscription").on("click", function() {
      if (!currentSubscriptionId) {
       showToast("Failed to cancel subscription. Please try again later.","danger");
        return;
      }

      // Show loading spinner
      $("#cancelSpinner").removeClass("d-none");
      $(this).prop("disabled", true);

      // Get selected cancellation timing
      const effectiveFrom = $("input[name='cancelTiming']:checked").val();

      // Call API to cancel subscription
      $.ajax({
        url: "/api/payments/cancel-subscription",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({
          subscriptionId: currentSubscriptionId,
          effectiveFrom: effectiveFrom
        }),
        success: function(response) {
          // Hide loading spinner
          $("#cancelSpinner").addClass("d-none");
          $("#confirmCancelSubscription").prop("disabled", false);

          if (response.success) {
            // Close modal
            $("#cancelSubscriptionModal").modal("hide");

            // Show success message and refresh subscription details
            showToast("Your subscription has been successfully cancelled.", "success");
            fetchSubscriptionDetails();
          } else {
            showToast("Error cancelling subscription: " + (response.error || "Unknown error"), "danger");
          }
        },
        error: function(xhr, status, error) {
          // Hide loading spinner
          $("#cancelSpinner").addClass("d-none");
          $("#confirmCancelSubscription").prop("disabled", false);

          console.error("Error cancelling subscription:", error);
          showToast("Failed to cancel subscription. Please try again later.", "danger");
        }
      });
    });



    //  Function to open the Paddle Customer Portal 
    function openManageSubscriptionPortal(customerId, subscriptionId) {
      if (!customerId || !subscriptionId) {
        showToast("Customer ID and  Subscription ID are missing . Please try again.", "danger");
        return;
      }

      // Show loading indicator
      showToast("Opening subscription management portal...", "info");

      // Call API to get the customer portal URL
      $.ajax({
        url: "/api/payments/create-portal-session",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({
          customer_id: customerId,
          subscription_id: subscriptionId
        }),
        success: function(response) {
          if (response.success && response.url) {
            // Open the customer portal in a new tab
            window.open(response.url, '_blank');

            
          } else {
            showToast("Failed to generate portal URL. Please try again later.", "danger");
          }
        },
        error: function(xhr, status, error) {
          console.error("Error getting manage subscription URL:", error);
          showToast("Failed to open subscription management portal. Please try again later.", "danger");
        }
      });
    }
    


    fetchPaddleToken();
    fetchSubscriptionDetails();
  });
</script>