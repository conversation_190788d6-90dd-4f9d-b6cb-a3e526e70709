const Design = require("../models/Design");
const sanitizer = require("sanitizer");

// Get all designs with pagination
exports.getDesignsPaginated = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const search = req.query.searchTerm || "";
    console.log(req.query, "searchTerm");

    //send own design only
    //const designs = await Design.find({creatorId: req.user._id }).sort({ updatedAt: -1 }).skip(skip).limit(limit).exec();

    const constructedQuery = search
      ? {
          designName: { $regex: search, $options: "i" },
          isDeleted: false,
          creatorId: req.user._id,
        }
      : { isDeleted: false, creatorId: req.user._id };
    const designs = await Design.find(constructedQuery)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();
    const totalDesigns = await Design.countDocuments(constructedQuery);

    res.status(200).json({
      page,
      limit,
      totalDesigns,
      designs,
      pagesTotal: Math.ceil(totalDesigns / limit),
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all designs with pagination from an associated business //used for admin
exports.getBusinessDesignsPaginated = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const designs = await Design.find({
      business: req.user.businessId,
      isDeleted: false,
      creatorId: req.user._id,
    })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();
    const totalDesigns = await Design.countDocuments();

    res.status(200).json({
      page,
      limit,
      totalDesigns,
      designs,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all designs without pagination
exports.getAllDesigns = async (req, res) => {
  try {
    // Fetch all designs, sorted by last updated first
    const designs = await Design.find({
      isDeleted: false,
      creatorId: req.user._id,
    })
      .sort({ updatedAt: -1 })
      .exec();

    // Get the total count of designs in the database
    const totalDesigns = await Design.countDocuments({
      isDeleted: false,
      creatorId: req.user._id,
    });

    // Return the response with all designs
    res.status(200).json({
      page: "1",
      limit: "0",
      totalDesigns, // Total number of designs in the collection
      designs, // The array of all designs
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get a single design by ID
exports.getDesignById = async (req, res) => {
  try {
    const design = await Design.findOne({
      _id: req.params.id,
      creatorId: req.user._id,
    });
    if (!design) {
      return res.status(404).json({ message: "Design not found" });
    }
    res.status(200).json(design);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create a new design
exports.createDesign = async (req, res) => {
  const {
    designName,
    startDate,
    endDate,
    location,
    url,
    canvas,
    pdfURL,
    imageURL,
    variables,
    businessId,
  } = req.body;

  const design = new Design({
    designName,
    startDate,
    endDate,
    location,
    url,
    canvas,
    pdfURL,
    imageURL,
    creatorId: req.user._id,
    variables,
    businessId,
  });

  try {
    const newDesign = await design.save();
    res.status(201).json(newDesign);
  } catch (error) {
    console.log("error saving design", error);
    res.status(400).json({ message: error.message });
  }
};

// Update a design by ID
exports.updateDesign = async (req, res) => {
  try {
    console.log("update design req received", req.body);
    const updatedDesign = await Design.findOneAndUpdate(
      { _id: req.params.id, creatorId: req.user._id },
      { $set: { designName: sanitizer.sanitize(req.body.designName || "") } },
      { new: true }
    );
    if (!updatedDesign) {
      return res.status(404).json({ message: "Design not found" });
    }
    res.status(200).json(updatedDesign);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete a design by ID
exports.updateDesignStatus = async (req, res) => {
  try {
    const deletedDesign = await Design.findOneAndUpdate(
      { _id: req.params.id, creatorId: req.user._id },
      { isDeleted: true },
      { new: true }
    );
    if (!deletedDesign) {
      return res.status(404).json({ message: "Design not found" });
    }
    res.status(200).json({ message: "Design deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Search designs by name or other filters
exports.searchDesigns = async (req, res) => {
  //const { searchTerm } = req.query;

  const searchTerm = req.query.query || "";
  //console.log("searchTerm...", searchTerm, +''+req.query)

  try {
    const designs = await Design.find({
      designName: { $regex: searchTerm, $options: "i" },
      isDeleted: false,
      creatorId: req.user._id,
    }).exec();

    res.status(200).json(designs);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Paginated search
exports.searchPaginated = async (req, res) => {
  try {
    const searchTerm = req.query.query || "";
    const searchCriteria = searchTerm
      ? {
          $or: [
            { designName: { $regex: searchTerm, $options: "i" } },
            { location: { $regex: searchTerm, $options: "i" } },
          ],
          isDeleted: false,
          creatorId: req.user._id,
        }
      : { isDeleted: false, creatorId: req.user._id };
    const designs = await Design.find(searchCriteria).exec();

    const totalResults = await Design.countDocuments(searchCriteria);

    res.status(200).json({
      totalResults,
      designs,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Make a copy of a design
exports.duplicateDesign = async (req, res) => {
  try {
    const designId = req.params.id;
    const originalDesign = await Design.findOne({
      _id: designId,
      creatorId: req.user._id,
    });

    if (!originalDesign) {
      return res.status(404).json({ message: "Design not found" });
    }

    // Create a copy
    const duplicatedDesign = new Design({
      designName: originalDesign.designName + " (Copy)",
      startDate: originalDesign.startDate,
      endDate: originalDesign.endDate,
      location: originalDesign.location,
      url: originalDesign.url,
      canvas: originalDesign.canvas,
      pdfURL: originalDesign.pdfURL,
      imageURL: originalDesign.imageURL,
      creatorId: originalDesign.creatorId,
      variables: originalDesign.variables,
      businessId: originalDesign.businessId,
      sharedTo: originalDesign.sharedTo,
    });

    const newDesign = await duplicatedDesign.save();
    res.status(201).json(newDesign);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getDeletedDesigns = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const designs = await Design.find({
      isDeleted: true,
      creatorId: req.user._id,
    })
      .skip(skip)
      .limit(limit)
      .exec();
    if (!designs) {
      return res.status(404).json({ message: "No deleted designs found" });
    }
    const totalDesigns = await Design.countDocuments({
      isDeleted: true,
      creatorId: req.user._id,
    });
    const totalPages = Math.ceil(totalDesigns / limit);
    res.status(200).json({
      designs,
      totalPages,
      totalDesigns,
      currentPage: page,
      limit,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.deleteDesign = async (req, res) => {
  try {
    const design = await Design.findOneAndDelete({
      _id: req.params.id,
      creatorId: req.user._id,
    });
    if (!design) {
      return res.status(404).json({ message: "Design not found" });
    }
    res.status(200).json({ message: "Design deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateDesignStatusRestore = async (req, res) => {
  try {
    const deletedDesign = await Design.findOneAndUpdate(
      { _id: req.params.id, creatorId: req.user._id },
      {
        isDeleted: false,
      }
    );
    if (!deletedDesign) {
      return res.status(404).json({ message: "Design not found" });
    }
    res.status(200).json({ message: "Design restored successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.bulkRestore = async (req, res) => {
  try {
    let designIds = req.body.designIds;
    if (!designIds) {
      return res.status(400).json({ message: "Invalid design IDs" });
    }
    designIds = Array.isArray(designIds) ? designIds : [designIds];

    await Design.updateMany(
      { _id: { $in: designIds }, creatorId: req.user._id },
      { isDeleted: false }
    );

    res.status(200).json({ message: "Designs restored successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.bulkDelete = async (req, res) => {
  try {
    let designIds = req.body.designIds;
    if (!designIds) {
      return res.status(400).json({ message: "Invalid design IDs" });
    }
    designIds = Array.isArray(designIds) ? designIds : [designIds];

    await Design.deleteMany({
      _id: { $in: designIds },
      creatorId: req.user._id,
    });

    res.status(200).json({ message: "Designs deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
