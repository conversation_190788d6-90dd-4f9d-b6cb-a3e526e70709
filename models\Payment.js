const mongoose = require("mongoose");

const PaymentSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  planId: { type: mongoose.Schema.Types.ObjectId, ref: "Plan", required: true },
  planName: {
    type: String,
    enum: ["Free", "Standard", "Professional", "Enterprise"],
    required: true,
  },
  certificateLimit: { type: Number, required: true },
  contactLimit: { type: Number, required: true },
  badgeLimit: { type: Number, required: true },
  eventLimit: { type: Number, required: true },

  paddleSubscriptionId: { type: String, required: true },
  paddleTransactionId: { type: String, required: true },

  billingCycle: {
    type: String,
    enum: ["monthly", "yearly"],
    default: "monthly",
  },
  totalAmountPaid: { type: Number, required: false },

  paymentStatus: {
    type: String,
    default: "pending",
  },
  paymentMethod: { type: String },
  customer_id: { type: String, required: true },
  startDate: { type: Date, default: Date.now },
  nextBillingDate: { type: Date },
  endDate: { type: Date },
  isAutoRenew: { type: Boolean, default: true },
  cancelledAt: { type: Date },

  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  isSubscriptionCancelled: { type: Boolean, default: false },
});

module.exports = mongoose.model("Payment", PaymentSchema);
