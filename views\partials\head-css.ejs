<!-- preloader css -->
<link rel="stylesheet" href="/assets/css/preloader.min.css" type="text/css" />

<!-- Bootstrap Css -->
<link href="/assets/css/bootstrap.min.css" id="bootstrap-style" rel="stylesheet" type="text/css" />
<!-- Icons Css -->
<link href="/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
<!-- App Css-->
<link href="/assets/css/app.min.css" id="app-style" rel="stylesheet" type="text/css" />

<!-- Early theme application to prevent flash -->
<script>
(function() {
    // Apply saved theme immediately to prevent flash
    const savedTheme = localStorage.getItem('theme-mode') || 'light';
    const body = document.body;

    if (savedTheme === 'dark') {
        body.setAttribute('data-bs-theme', 'dark');
        body.setAttribute('data-topbar', 'dark');
        body.setAttribute('data-sidebar', 'dark');
    } else {
        body.setAttribute('data-bs-theme', 'light');
        body.setAttribute('data-topbar', 'light');
        body.setAttribute('data-sidebar', 'light');
    }
})();
</script>