<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title":"Scripts Settings" }) %>

<%- contentFor('body') %>
<%-include("partials/page-title", {"title":"Scripts Settings", "pagetitle":"Scripts Settings" }) %>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Header & Footer Scripts</h4>
                <p class="card-title-desc">Add custom JavaScript code for analytics, tracking, and other third-party integrations. These scripts will be loaded on all public pages.</p>
            </div>
            <div class="card-body">
                <form id="scriptsForm">
                    <!-- Header Scripts Section -->
                    <div class="mb-4">
                        <label for="headerScripts" class="form-label">
                            <i class="fas fa-code me-2"></i><PERSON>er Scripts
                        </label>
                        <p class="text-muted small mb-2">Scripts added here will be loaded in the &lt;head&gt; section of all public pages. Perfect for analytics tracking codes like Google Analytics, Facebook Pixel, etc.</p>
                        <textarea
                            class="form-control"
                            id="headerScripts"
                            name="headerScripts"
                            rows="8"
                            placeholder="<script>
// Example: Google Analytics
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

ga('create', 'UA-XXXXX-Y', 'auto');
ga('send', 'pageview');
</script>"></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Make sure to include proper &lt;script&gt; tags. These scripts will be executed when the page loads.
                        </div>
                    </div>

                    <!-- Footer Scripts Section -->
                    <div class="mb-4">
                        <label for="footerScripts" class="form-label">
                            <i class="fas fa-code me-2"></i>Footer Scripts
                        </label>
                        <p class="text-muted small mb-2">Scripts added here will be loaded before the closing &lt;/body&gt; tag. Ideal for chat widgets, conversion tracking, and other scripts that should load after page content.</p>
                        <textarea
                            class="form-control"
                            id="footerScripts"
                            name="footerScripts"
                            rows="8"
                            placeholder="<script>
// Example: Chat widget or conversion tracking
(function() {
    var script = document.createElement('script');
    script.src = 'https://example.com/widget.js';
    script.async = true;
    document.body.appendChild(script);
})();
</script>"></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Footer scripts load after the page content, which can improve page load performance.
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-primary" style="background-color: #5156be; border-color: #5156be;">
                                <i class="fas fa-save me-2"></i>Save Scripts
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" id="previewBtn">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-danger" id="clearBtn">
                                <i class="fas fa-trash me-2"></i>Clear All
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Loading Indicator -->
                <div id="loadingIndicator" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Saving scripts...</p>
                </div>

                <!-- Success/Error Messages -->
                <div id="alertContainer" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #5156be; color: white;">
                <h5 class="modal-title" id="previewModalLabel">
                    <i class="fas fa-eye me-2"></i>Scripts Preview
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Header Scripts:</h6>
                        <pre id="headerPreview" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">Footer Scripts:</h6>
                        <pre id="footerPreview" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Clear Confirmation Modal -->
<div class="modal fade" id="clearModal" tabindex="-1" aria-labelledby="clearModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #dc3545; color: white;">
                <h5 class="modal-title" id="clearModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Clear
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to clear all header and footer scripts? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmClear">
                    <i class="fas fa-trash me-2"></i>Clear All Scripts
                </button>
            </div>
        </div>
    </div>
</div>

<%- contentFor('FooterJs') %>
<script>
$(document).ready(function() {
    let businessId = null;

    // Get business ID from user profile
    function getBusinessId() {
        return $.ajax({
            url: '/api/users/profile',
            method: 'GET',
            success: function(response) {
                businessId = response.businessId;
                loadCurrentScripts();
            },
            error: function(xhr, status, error) {
                console.error('Error fetching business ID:', error);
                showAlert('Error loading business information', 'danger');
            }
        });
    }

    // Load current scripts
    function loadCurrentScripts() {
        if (!businessId) return;

        $.ajax({
            url: `/api/business/${businessId}`,
            method: 'GET',
            success: function(response) {
                $('#headerScripts').val(response.headerScripts || '');
                $('#footerScripts').val(response.footerScripts || '');
            },
            error: function(xhr, status, error) {
                console.error('Error loading scripts:', error);
                showToast('Error loading current scripts', 'warning');
            }
        });
    }

    // Save scripts
    $('#scriptsForm').on('submit', function(e) {
        e.preventDefault();

        if (!businessId) {
            showToast('Business ID not found. Please refresh the page.', 'danger');
            return;
        }

        const headerScripts = $('#headerScripts').val().trim();
        const footerScripts = $('#footerScripts').val().trim();

        // Show loading
        $('#loadingIndicator').show();
        $('button[type="submit"]').prop('disabled', true);

        $.ajax({
            url: `/api/business/${businessId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                headerScripts: headerScripts,
                footerScripts: footerScripts
            }),
            success: function(response) {
                showToast('Scripts saved successfully! Changes will be visible on all public pages.', 'primary');
            },
            error: function(xhr, status, error) {
                console.error('Error saving scripts:', error);
                const errorMessage = xhr.responseJSON?.error || 'Failed to save scripts';
               
            },
            complete: function() {
                $('#loadingIndicator').hide();
                $('button[type="submit"]').prop('disabled', false);
            }
        });
    });

    // Preview scripts
    $('#previewBtn').on('click', function() {
        const headerScripts = $('#headerScripts').val().trim();
        const footerScripts = $('#footerScripts').val().trim();

        $('#headerPreview').text(headerScripts || 'No header scripts added');
        $('#footerPreview').text(footerScripts || 'No footer scripts added');

        $('#previewModal').modal('show');
    });

    // Clear scripts
    $('#clearBtn').on('click', function() {
        $('#clearModal').modal('show');
    });

    $('#confirmClear').on('click', function() {
        $('#headerScripts').val('');
        $('#footerScripts').val('');
        $('#clearModal').modal('hide');
        
    });

   

    // Initialize
    getBusinessId();
});
</script>